//EISDEVTS-39836
Entity CapDentalSettlementEntity is CapSettlement {

    @Description("Searchable settlement by loss ")
    @Searchable
    ExtLink claimLossIdentification: RootEntity

    //EISDEVTS-40103
    Ref settlementDetail: CapDentalSettlementDetailEntity

    //EISDEVTS-40103
    @NonComparable
    Attr settlementLossInfo: CapDentalClaimInfoEntity

    //EISDEVTS-40103
    @NonComparable
    Attr settlementResult: CapDentalDecisionResultEntity
}

//EISDEVTS-39836
Entity CapDentalSettlementDetailEntity is CapSettlementDetail {

    //EISDEVTS-40103
    Attr claimOverride: CapDentalClaimOverrideEntity

    //EISDEVTS-40715
    @Description("EOB Free Form Message.")
    Attr eobFreeFormMessage: String

    //EISDEVTS-40103
    @Description("NOT USED")
    @NonComparable
    Attr overrideCd: String

    //EISDEVTS-40103
    Attr serviceOverrides: *CapDentalServiceOverrideEntity
}
//EISDEVTS-39836
Entity CapDentalResultEntryEntity {

    //EISDEVTS-40103
    @NonComparable
    Attr calculationResult: CapDentalCalculationResultEntity

    //EISDEVTS-52795
    @Description("Object for reserved accumulators details.")
    @NonComparable
    Attr reservedAccumulators: *CapDentalAccumulatorEntity

    //EISDEVTS-40103
    @Description("This link to Procedure from Intake. Defines which response is associated to which submited procedure.")
    @NonComparable
    Attr serviceSource: String

    //EISDEVTS-40103
    @NonComparable
    Attr status: CapDentalCalculationStatusEntity
}
//EISDEVTS-39836
Entity CapDentalDecisionResultEntity is CapSettlementResult {

    //EISDEVTS-40103
    @Max(999)
    @Min(1)
    @NonComparable
    Attr entries: *CapDentalResultEntryEntity

    //EISDEVTS-50586
    @Description("Entity that holds informational message details")
    @NonComparable
    Attr messages: *MessageType

    //EISDEVTS-40103
    @Description("Provider or insured or alternate payee reference for payment generation.")
    @NonComparable
    Attr payeeRef: String

    //EISDEVTS-40103
    @Description("Final amount for payment generation.")
    @NonComparable
    Attr paymentAmount: Money

    //EISDEVTS-40103
    @Description("Proposal code which identifies next steps for system and/or user: Additional Reviewer or generate payment.")
    @NonComparable
    Attr proposal: String
}
//EISDEVTS-39836
Entity CapDentalClaimInfoEntity is CapSettlementLossInfo {

    //EISDEVTS-40103
    @NonComparable
    Attr claimData: CapDentalClaimDataEntity

    //EISDEVTS-40103
    @NonComparable
    Attr patient: CapDentalPatientEntity

    //EISDEVTS-40103
    @Description("Claim received date.")
    @NonComparable
    Attr receivedDate: Date

    //EISDEVTS-40103
    @Description("Source type EDI or NONEDI.")
    @NonComparable
    Attr source: String

    //EISDEVTS-40103
    @KrakenChildContext
    @KrakenField
    @NonComparable
    Attr submittedProcedures: *CapDentalProcedureEntity
}
//EISDEVTS-39836
Entity CapDentalRuleInputEntity {

    //EISDEVTS-40103
    @NonComparable
    Attr details: CapDentalSettlementDetailEntity

    //EISDEVTS-40103
    @NonComparable
    Attr loss: CapDentalClaimInfoEntity

    //GENESIS-354053
    @NonComparable
    Attr entryPreviouslyUsed: *CapDentalEntryPreviouslyUsedEntity
}
//EISDEVTS-39836
Entity CapDentalCalculationResultEntity {

    //EISDEVTS-40103
    @Description("Calculation Result Allowed fee amount.")
    @NonComparable
    Attr allowedFee: Money

    //EISDEVTS-40103
    @Description("Charge amount.")
    @NonComparable
    Attr charge: Money

    //EISDEVTS-40103
    @Description("Coinsurance amount if exists.")
    @NonComparable
    Attr coinsuranceAmt: Money

    //EISDEVTS-40103
    @Description("Coinsurance percentage if exists.")
    @NonComparable
    Attr coinsurancePercentage: Decimal

    //EISDEVTS-40103
    @Description("Calculation Result Considered fee amount.")
    @NonComparable
    Attr consideredFee: Money

    //EISDEVTS-40103
    @Description("Contribition to Maximum Out of Pocket amount.")
    @NonComparable
    Attr contributionToMOOP: Money

    //EISDEVTS-40103
    @Description("Copay amount.")
    @NonComparable
    Attr copay: Money

    //EISDEVTS-40103
    @Description("Procedure code received after adjudication")
    @NonComparable
    Attr coveredCode: String

    //EISDEVTS-40103
    @Description("Calculation Result Covered fee amount.")
    @NonComparable
    Attr coveredFee: Money

    //EISDEVTS-40103
    @Description("Net Benefit amount.")
    @NonComparable
    Attr netBenefitAmount: Money

    //EISDEVTS-40103
    @Description("Patient responsibility amount.")
    @NonComparable
    Attr patientResponsibility: Money

    //EISDEVTS-40103
    @Description("Amont payable by the insured .")
    @NonComparable
    Attr payableDeductible: Money

    //EISDEVTS-40103
    @Description("Calculation Result Procedure ID.")
    @NonComparable
    Attr procedureID: String

    //EISDEVTS-40103
    @Description("Covered Procedure area calculated after adjudication..")
    @NonComparable
    Attr procedureType: String

    //EISDEVTS-40103
    @Description("Calculation Result Submitted Code.")
    @NonComparable
    Attr submittedCode: String

    @Description("Amount covered in this fee schedule.")
    @NonComparable
    Attr coveredFeeSchedule: Money
}
//EISDEVTS-39836
Entity CapDentalCalculationStatusEntity {

    //EISDEVTS-40103
    @Description("Calculation code.")
    @NonComparable
    Attr code: String

    //EISDEVTS-40103
    @Description("Calculation Status Covered code.")
    @NonComparable
    Attr coveredCode: String

    //EISDEVTS-40103
    @Description("Considered fee.")
    @NonComparable
    Attr fee: Money

    //EISDEVTS-40103
    @Description("Status flag - Denied, Allowed, Review Required...")
    @NonComparable
    Attr flag: String

    //EISDEVTS-40103
    @Description("Calculation percentage.")
    @NonComparable
    Attr percentage: Decimal

    //EISDEVTS-40555
    @Description("Claim number of the history procedure if the service is predet-preauth")
    @NonComparable
    Attr preauthorizationNumber: String

    //EISDEVTS-40103
    @Description("Predentermined indicator.")
    @NonComparable
    Attr predetInd: Boolean

    //EISDEVTS-40103
    @Description("Calculation Status Procedure ID.")
    @NonComparable
    Attr procedureID: String

    //EISDEVTS-40103
    @Description("Question for the procedure.")
    @NonComparable
    Attr questions: *String

    //EISDEVTS-40103
    @Description("Calculation Status Reason Code.")
    @NonComparable
    Attr reasonCode: String

    //EISDEVTS-47459
    @NonComparable
    Attr remarkMessages: *CapDentalRemarkMessageEntity

    //EISDEVTS-40103
    @Description("Calculation Status Reason.")
    @NonComparable
    Attr statusReason: String

    //EISDEVTS-40103
    @Description("Calculation Status Submitted procedure code.")
    @NonComparable
    Attr submittedCode: String
}
//EISDEVTS-39836
Entity CapDentalProcedureEntity is CapDentalBaseProcedure {

    //EISDEVTS-39870
    @Description("Individual Policy details.")
    @NonComparable
    Attr certPolicyInfo: CapDentalPolicyInfoEntity

    //EISDEVTS-40103
    @NonComparable
    Attr consultantReview: CapDentalConsultantReviewEntity

    //EISDEVTS-40103
    @Description("Amount covered in this fee schedule.")
    @NonComparable
    Attr coveredFeeSchedule: Money

    //EISDEVTS-40103
    @Description("Amount covered for Usual Customary and Reasonable fee.")
    @NonComparable
    Attr coveredFeeUCR: Money

    //EISDEVTS-40103
    @NonComparable
    Attr coveredFeeUCRME: Money

    //EISDEVTS-40103
    @NonComparable
    Attr dentist: CapDentalDentistEntity

    //EISDEVTS-40103
    @NonComparable
    Attr feeSchedule: *CapDentalFeeRateEntity

    //EISDEVTS-40103
    @NonComparable
    Attr feeUCR: *CapDentalFeeRateEntity

    //EISDEVTS-40103
    @NonComparable
    Attr feeUCRME: *CapDentalFeeRateEntity

    //EISDEVTS-40103
    @NonComparable
    Attr frequencyDHMO: CapDentalDHMOFrequencyEntity

    //EISDEVTS-40103
    @NonComparable
    Attr frequencyPPO: CapDentalPPOFrequencyEntity

    //EISDEVTS-40555
    @Description("Claim number of history procedure.")
    @NonComparable
    Attr lossNumber: String

    //EISDEVTS-39867
    @Description("Master Policy details.")
    @NonComparable
    Attr masterPolicyInfo: *CapDentalPolicyInfoEntity

    //EISDEVTS-40103
    @Description("Amount paid in previous procedures.")
    @NonComparable
    Attr paidAmount: Money

    //EISDEVTS-40103
    @Description("Historical Procedures.")
    @NonComparable
    Attr procedureStatus: String

    //EISDEVTS-40103
    @NonComparable
    Attr status: CapDentalCalculationStatusEntity

    //EISDEVTS-40103
    @Description("Amount submitted in this fee schedule.")
    @NonComparable
    Attr submittedFeeSchedule: Money

    //EISDEVTS-40103
    @Description("Amount submitted for Usual Customary and Reasonable fee.")
    @NonComparable
    Attr submittedFeeUCR: Money

    //EISDEVTS-40103
    @NonComparable
    Attr submittedFeeUCRME: Money
}

Entity CapDentalClaimDataEntity is CapDentalBaseClaimData {

}
//EISDEVTS-39836
Entity CapDentalPatientEntity {

    //EISDEVTS-40103
    @Description("Object for remaining accumulators details.")
    @NonComparable
    Attr accumulators: *CapDentalAccumulatorEntity

    //EISDEVTS-39868
    @Description("Patient's date of birth.")
    @NonComparable
    Attr birthDate: Date

    //EISDEVTS-39868
    @Description("Patient's disabilities.")
    @NonComparable
    Attr disabilities: *String

    //EISDEVTS-40103
    @Description("Patient history procedures.")
    @NonComparable
    Attr historyProcedures: *CapDentalProcedureEntity

    //EISDEVTS-40103
    @Description("Patient's ID.")
    @NonComparable
    Attr patientID: String

    //EISDEV-353761
    @Description("Patient's registryId.")
    @NonComparable
    Attr registryId: String
}
//EISDEVTS-39836
@UnverifiedPolicy(["Dental"])
Entity CapDentalPolicyInfoEntity is CapPolicyInfo {

    //EISDEVTS-39870
    @Description("Is late entrant benefit period waiting applied.")
    @NonComparable
    Attr applyLateEntrantBenefitWaitingPeriods: Boolean

    //EISDEVTS-40103
    @Description("Basic waiting period.")
    @NonComparable
    Attr basicWaitingPeriod: String

    //EISDEVTS-39870
    @Description("Child Max Age limit.")
    @NonComparable
    Attr childMaxAgeCd: String

    //EISDEVTS-39870
    @Description("Coinsurances percentages details.")
    @NonComparable
    Attr coinsurances: *CapDentalPolicyInfoCoinsuranceEntity

    //EISDEVTS-40381
    @Description("Dental Dedudictble details.")
    @NonComparable
    Attr deductibleDetails: *CapDeductibleDetailEntity

    //EISDEVTS-40381
    @Description("Dental Maximums details.")
    @NonComparable
    Attr dentalMaximums: *CapDentalMaximumEntity

    //EISDEVTS-39870
    @Description("Full time student Age limit.")
    @NonComparable
    Attr fullTimeStudentAgeCd: String

    //EISDEVTS-40103
    @Description("Implants waiting period.")
    @NonComparable
    Attr implantsWaitingPeriod: String

    //EISDEVTS-39870
    @Description("Insured details.")
    @NonComparable
    Attr insureds: *CapDentalPolicyInfoInsuredDetailsEntity

    //EISDEVTS-48227
    @Description("Implants Maximum Applies toward Plan Maximum")
    @NonComparable
    Attr isImplantsMaximumAppliedTowardPlanMaximum: Boolean

    //EISDEVTS-39870
    @Description("Late Entrant Waiting Period details.")
    @NonComparable
    Attr lateEntrantWaitingPeriodsDetails: *CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity

    //EISDEVTS-40103
    @Description("Major waiting period.")
    @NonComparable
    Attr majorWaitingPeriod: String

    //EISDEVTS-40103
    @Description("Ortho In Network Coinsurance Percentage.")
    @NonComparable
    Attr orthoINNCoinsurancePercent: Decimal

    //EISDEVTS-40103
    @Description("Ortho Out of Network Coinsurance Percentage.")
    @NonComparable
    Attr orthoOONCoinsurancePercent: Decimal

    //EISDEVTS-40103
    @Description("Ortho waiting period.")
    @NonComparable
    Attr orthoWaitingPeriod: String

    //EISDEVTS-40103
    @Description("Primary Care Dentist ID.")
    @NonComparable
    Attr pcdId: String

    //EISDEVTS-40103
    @Description("PCD Assignment Effective/Termination Period.")
    @NonComparable
    Attr pcdTerm: CapDentalTermEntity

    //EISDEVTS-40103
    @Description("Plan type for the policy.")
    @NonComparable
    Attr plan: String

    //EISDEVTS-39867
    @Description("PPO/DHMO product.")
    @NonComparable
    Attr planCategory: String

    //EISDEVTS-39870
    @Description("Plan Name.")
    @NonComparable
    Attr planName: String

    //EISDEVTS-40103
    @Description("Date which the Policy is paid up to.")
    @NonComparable
    Attr policyPaidToDate: Date

    //EISDEVTS-40103
    @Description("Date which the Policy is paid up to with Grace period.")
    @NonComparable
    Attr policyPaidToDateWithGracePeriod: Date

    //EISDEVTS-40103
    @Description("Preventetive waiting period.")
    @NonComparable
    Attr preventWaitingPeriod: String

    //EISDEVTS-39835
    @NonComparable
    Attr unverifiedInfo: CapDentalUnverifiedInfoEntity

    @Description("Identification number of the policy in CAP subsystem.")
    @NonComparable
    @Label("Policy")
    Attr capPolicyId: String

    @Description("Defines policy version stored on CAP side.")
    @NonComparable
    @Label("Policy Version")
    Attr capPolicyVersionId: String

    @Description("Defines policy Enrollment TypeCd.")
    @NonComparable
    Attr enrollmentTypeCd: String

    @Description("Defines policy Coverage Eligibility.")
    @NonComparable
    Attr coverageEligibility: CapPolicyEligibilityEntity

    @Description("Defines policy Waiting Period Apply To.")
    @NonComparable
    Attr waitingPeriodApplyTo: String

    //GENESIS-353761
    @Description("Frequency Limitations.")
    @Label("Frequency Limitations")
    @NonComparable
    Attr frequencyLimitations: CapPolicyFrequencyLimitationsEntity

    //GENESIS-353761
    @Description("Service Category.")
    @Label("Service Category")
    @NonComparable
    Attr serviceCategory: CapPolicyServiceCategoryEntity

    @Description("Ortho Deductible Type.")
    @Label("Ortho Deductible Type")
    @NonComparable
    Attr orthoDeductibleType: String

    @Description("TMJ Deductible Type.")
    @Label("TMJ Deductible Type")
    @NonComparable
    Attr tmjDeductibleType: String

    @Description("Cosmetic Deductible Type.")
    @Label("Cosmetic Deductible Type")
    @NonComparable
    Attr cosmeticDeductibleType: String

    @Description("Orthodontic Late Entrant Waiting Period.")
    @NonComparable
    Attr orthoLateEntrantWaitingPeriod: String

    @Description("Cosmetic Waiting Period.")
    @NonComparable
    Attr cosmeticWaitingPeriod: String

    @Description("Included Ortho Coverage.")
    @NonComparable
    Attr isOrthoCoverageIncluded: Boolean

    @Description("Included Implant Coverage.")
    @NonComparable
    Attr isImplantCoverageIncluded: Boolean

    @Description("Included Tmj Coverage.")
    @NonComparable
    Attr isTmjCoverageIncluded: Boolean

    @Description("Included Cosmetic Services.")
    @NonComparable
    Attr isCosmeticServicesIncluded: Boolean

    @Description("Ortho Availability")
    @NonComparable
    Attr orthoAvailability: String

    @Description("Ortho Child Age Limit")
    @NonComparable
    Attr orthoChildAgeLimit: Integer
}

Entity CapPolicyEligibilityEntity {

    @Description("Waiting period amount")
    @NonComparable
    Attr waitingPeriodAmount: Integer

    @Description("Waiting period mode code")
    @NonComparable
    Attr waitingPeriodModeCd: String

    @Description("Eligibility type code")
    @NonComparable
    Attr eligibilityTypeCd: String

    @Description("Waiting Period Def Code")
    @NonComparable
    Attr waitingPeriodDefCd: String
}

//EISDEVTS-39836
Entity CapDentalUnverifiedInfoEntity {

    //EISDEVTS-39835
    @Description("Claim Without Policy Employer Name.")
    @NonComparable
    Attr employerName: String

    //EISDEVTS-39835
    @Description("Claim Without Policy Group Number.")
    @NonComparable
    Attr groupNumber: String
}
//EISDEVTS-39836
Entity CapDentalAccumulatorEntity {

    //EISDEVTS-40103
    @Description("Type for the Maximum.")
    @NonComparable
    Attr accumulatorType: String

    //EISDEVTS-43037
    @Description("Defines to which procedure category maximum applies to.")
    @NonComparable
    @Deprecated
    Attr appliesToProcedureCategory: String

    //EISDEVTS-43037
    @Description("Network type for maximum.")
    @NonComparable
    Attr networkType: String

    //EISDEVTS-43037
    @Description("Remaining maximum in network amount.")
    @NonComparable
    Attr remainingAmount: Money

    //EISDEVTS-43037
    @Description("Renewal type for maximum.")
    @NonComparable
    Attr renewalType: String

    //EISDEVTS-43037
    @Description("Reserved Amount of Maximum.")
    @NonComparable
    Attr reservedAmount: Money

    @Description("Defines to which procedure category(s) maximum/deductible applies to.")
    @NonComparable
    Attr appliesToProcedureCategories: *String

    @Description("Accumulator Term.")
    @NonComparable
    Attr term: Term
}
//EISDEVTS-39836
Entity CapDentalDentistEntity {

    //EISDEVTS-40103
    @Description("Dentist National Provider ID.")
    @NonComparable
    Attr dentistID: String

    //EISDEVTS-40103
    @Description("Dentist specialities.")
    @NonComparable
    Attr dentistSpecialties: *String

    //EISDEVTS-40103
    @Description("Dentist fee schedule type.")
    @NonComparable
    Attr feeScheduleType: String

    //EISDEVTS-40103
    @Description("If the dentist In or Out of Network.")
    @NonComparable
    Attr inOutNetwork: String

    //EISDEVTS-40103
    @Description("Primary care dentist in DHMO.")
    @NonComparable
    Attr pcdID: String

    //EISDEVTS-40103
    @Description("Dentist practice term.")
    @NonComparable
    Attr practiceTerm: Term

    //EISDEVTS-40103
    @Description("Dentist practice type.")
    @NonComparable
    Attr practiceType: String

    //EISDEVTS-40103
    @Description("Provider tax identification number.")
    @NonComparable
    Attr providerTIN: String
}
//EISDEVTS-39836
Entity CapDentalPPOFrequencyEntity {

    //EISDEVTS-40103
    @Description("PPO number of times procedure allowed.")
    @NonComparable
    Attr frequency: String

    //EISDEVTS-40103
    @Description("PPO frequency comment.")
    @NonComparable
    Attr frequencyComment: String

    //EISDEVTS-40103
    @Description("PPO frequency period.")
    @NonComparable
    Attr frequencyPeriod: Integer

    //EISDEVTS-40103
    @Description("PPO frequency period type.")
    @NonComparable
    Attr frequencyPeriodType: String

    //EISDEVTS-40103
    @Description("PPO Range of the frequency.")
    @NonComparable
    Attr frequencyRange: String

    //EISDEVTS-40103
    @Description("PPO rule to determine if procedure allowed.")
    @NonComparable
    Attr frequencyRule: String

    //EISDEVTS-40103
    @Description("PPO procedure type for the frequency.")
    @NonComparable
    Attr procedureType: String
}
//EISDEVTS-39836
Entity CapDentalDHMOFrequencyEntity {

    //EISDEVTS-40103
    @Description("DHMO number of times procedure allowed.")
    @NonComparable
    Attr frequency: String

    //EISDEVTS-40103
    @Description("DHMO frequency period.")
    @NonComparable
    Attr frequencyPeriod: Integer

    //EISDEVTS-40103
    @Description("DHMO frequency period type.")
    @NonComparable
    Attr frequencyPeriodType: String

    //EISDEVTS-40103
    @Description("DHMO Range of the frequency.")
    @NonComparable
    Attr frequencyRange: String

    //EISDEVTS-40103
    @Description("DHMO applied toward the Maximum Out of Pocket value.")
    @NonComparable
    Attr isAppliedTowardsMOOP: String

    //EISDEVTS-40103
    @Description("If the DHMO covered or not.")
    @NonComparable
    Attr isCovered: Boolean
}
//EISDEVTS-39836
Entity CapDentalFeeRateEntity {

    //EISDEVTS-40103
    @Description("Fee rate amount for the code.")
    @NonComparable
    Attr feeAmount: Money

    //EISDEVTS-40103
    @Description("Code that fee applied to.")
    @NonComparable
    Attr feeCode: String
}
//EISDEVTS-39836
Entity CapDentalConsultantReviewEntity {

    //EISDEVTS-40103
    @Description("Consultant review alternate CDT code.")
    @NonComparable
    Attr alternateCDTCode: String

    //EISDEVTS-40103
    @Description("Consultant review reply.")
    @NonComparable
    Attr consultantReply: String

    //EISDEVTS-40103
    @Description("Consultant review reply letter.")
    @NonComparable
    Attr consultantReplyLetter: String

    //EISDEVTS-40103
    @Description("Surface which had consultant review.")
    @NonComparable
    Attr surface: String
}
//EISDEVTS-39836
Entity CapDentalClaimOverrideEntity {

    //EISDEVTS-40103
    Attr bypassClaimLogic: CapDentalBypassClaimLogicEntity

    //EISDEVTS-40103
    @Description("Allow Service.")
    Attr isAllowed: Boolean

    //EISDEVTS-40103
    @Description("Deny Service.")
    Attr isDenied: Boolean

    //EISDEVTS-40103
    Attr overrideClaimValue: CapDentalOverrideClaimValueEntity

    //EISDEVTS-40103
    @Description("Suppress EOB for Member.")
    Attr suppressMemberEob: Boolean

    //EISDEVTS-40103
    @Description("Suppress EOB for Provider.")
    Attr suppressProviderEob: Boolean

    //EISDEVTS-40103
    Attr waiveClaimValue: CapDentalWaiveClaimValueEntity
}
//EISDEVTS-39836
Entity CapDentalServiceOverrideEntity {

    //EISDEVTS-40103
    Attr bypassServiceLogic: CapDentalBypassServiceLogicEntity

    //EISDEVTS-40103
    @Description("Allow Service.")
    Attr isAllowed: Boolean

    //EISDEVTS-40103
    @Description("Deny Service.")
    Attr isDenied: Boolean

    //EISDEVTS-40103
    @Description("Override Remark.")
    Attr overrideRemark: String

    //EISDEVTS-40103
    Attr overrideServiceValue: CapDentalOverrideServiceValueEntity

    //EISDEVTS-40103
    @Description("Link to Service.")
    Attr serviceSource: String

    //EISDEVTS-40103
    @Description("Suppress EOB for Member.")
    Attr suppressMemberEob: Boolean

    //EISDEVTS-40103
    @Description("Suppress EOB for Provider.")
    Attr suppressProviderEob: Boolean

    //EISDEVTS-40103
    Attr waiveServiceValue: CapDentalWaiveServiceValueEntity
}
//EISDEVTS-39836
Entity CapDentalWaiveClaimValueEntity {

    //EISDEVTS-40103
    @Description("Waive Basic Services Waiting Period.")
    Attr waiveBasicWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Deductible.")
    Attr waiveDeductible: Boolean

    //EISDEVTS-40103
    @Description("Waive Eligibility After Coverage Start Date.")
    Attr waiveEligibilityAfterStartDate: Boolean

    //EISDEVTS-40103
    @Description("Waive Eligibility Prior to Coverage Start Date.")
    Attr waiveEligibilityPriorStartDate: Boolean

    //EISDEVTS-40103
    @Description("Waive Late Entrant Waiting Period.")
    Attr waiveLateEntrantWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Major Services Waiting Period.")
    Attr waiveMajorWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Maximum Amount Limits.")
    Attr waiveMaximumLimitAmounts: Boolean

    //EISDEVTS-40103
    @Description("Waive Ortho Services Waiting Period.")
    Attr waiveOrthoWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Preventive Services Waiting Period.")
    Attr waivePreventiveWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Replacement Limit.")
    Attr waiveReplacementLimit: Boolean

    //EISDEVTS-40103
    @Description("Waive Service Frequency Limit.")
    Attr waiveServiceFrequencyLimit: Boolean
}
//EISDEVTS-39836
Entity CapDentalBypassClaimLogicEntity {

    //EISDEVTS-40103
    @Description("Bypass All Rules and Dental Review Logic.")
    Attr bypassAllRules: Boolean

    //EISDEVTS-40103
    @Description("Bypass Clinical/Consultant Review.")
    Attr bypassClinicalReview: Boolean

    //EISDEVTS-40103
    @Description("Bypass COB Logic.")
    Attr bypassCobLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Duplicate Service Logic.")
    Attr bypassDuplicateServiceLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Grace Period Logic.")
    Attr bypassGracePeriodLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Interest Logic.")
    Attr bypassInterestLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Missing Tooth Exclusion Logic.")
    Attr bypassMissingToothLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Overpayment Recoupment Logic.")
    Attr bypassOverpaymentLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Tooth Extraction Rules.")
    Attr bypassToothExtractionRules: Boolean
}
//EISDEVTS-39836
Entity CapDentalOverrideClaimValueEntity {

    //EISDEVTS-40103
    @Description("Override Basic Services Waiting Period.")
    Attr overrideBasicWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Eligibility Period.")
    Attr overrideEligibilityPeriod: Period

    //EISDEVTS-40103
    @Description("Override Fee Schedule.")
    Attr overrideFeeSchedule: String

    //EISDEVTS-40103
    @Description("Override Grace Period.")
    Attr overrideGracePeriod: Integer

    //EISDEVTS-40103
    @Description("Override Late Entrant Waiting Period.")
    Attr overrideLateEntrantWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Major Services Waiting Period.")
    Attr overrideMajorWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Ortho Services Waiting Period.")
    Attr overrideOrthoWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Payment Interest Amount.")
    Attr overridePaymentInterestAmount: Money

    //EISDEVTS-40103
    @Description("Override Payment Interest Number of Days.")
    Attr overridePaymentInterestDays: Integer

    //EISDEVTS-40103
    @Description("Override Payment Interest State.")
    Attr overridePaymentInterestState: String

    //EISDEVTS-40103
    @Description("Override Preventive Services Waiting Period.")
    Attr overridePreventiveWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Student Dependent Status.")
    Attr overrideStudentDependentStatus: String
}
//EISDEVTS-39836
Entity CapDentalWaiveServiceValueEntity {

    //EISDEVTS-40103
    @Description("Waive Deductible.")
    Attr waiveDeductible: Boolean

    //EISDEVTS-40103
    @Description("Waive Eligibility After Coverage Start Date.")
    Attr waiveEligibilityAfterStartDate: Boolean

    //EISDEVTS-40103
    @Description("Waive Eligibility Prior to Coverage Start Date.")
    Attr waiveEligibilityPriorStartDate: Boolean

    //EISDEVTS-40103
    @Description("Waive Late Entrant Waiting Period.")
    Attr waiveLateEntrantWaitingPeriod: Boolean

    //EISDEVTS-40103
    @Description("Waive Maximum Amount Limits.")
    Attr waiveMaximumLimitAmounts: Boolean

    //EISDEVTS-40103
    @Description("Waive Replacement Limit.")
    Attr waiveReplacementLimit: Boolean

    //EISDEVTS-40103
    @Description("Waive Service Frequency Limit.")
    Attr waiveServiceFrequencyLimit: Boolean

    //EISDEVTS-40103
    @Description("Waive Service Category Waiting Period.")
    Attr waiveServiceWaitingPeriod: Boolean
}
//EISDEVTS-39836
Entity CapDentalBypassServiceLogicEntity {

    //EISDEVTS-40103
    @Description("Bypass All Rules and Dental Review Logic.")
    Attr bypassAllRules: Boolean

    //EISDEVTS-40103
    @Description("Bypass Clinical/Consultant Review.")
    Attr bypassClinicalReview: Boolean

    //EISDEVTS-40103
    @Description("Bypass COB Logic.")
    Attr bypassCobLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Duplicate Service Logic.")
    Attr bypassDuplicateServiceLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Grace Period Logic.")
    Attr bypassGracePeriodLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Interest Logic.")
    Attr bypassInterestLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Missing Tooth Exclusion Logic.")
    Attr bypassMissingToothLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Overpayment Recoupment Logic.")
    Attr bypassOverpaymentLogic: Boolean

    //EISDEVTS-40103
    @Description("Bypass Tooth Extraction Rules.")
    Attr bypassToothExtractionRules: Boolean
}
//EISDEVTS-39836
Entity CapDentalOverrideServiceValueEntity {

    //EISDEVTS-40103
    @Description("Override DHMO Allowed Amount.")
    Attr overrideAllowedAmount: Money

    //EISDEVTS-40103
    @Description("Override COB Applied.")
    Attr overrideCobApplied: Money

    //EISDEVTS-40103
    @Description("Override Coinsurance %")
    Attr overrideCoinsurancePct: Decimal

    //EISDEVTS-40103
    @Description("Override Maximum amount that will be considered for the service.")
    Attr overrideConsideredAmount: Money

    //EISDEVTS-40103
    @Description("Override Copay Amount.")
    Attr overrideCopayAmount: Money

    //EISDEVTS-40103
    @Description("The override amount that will be applied to the service.")
    Attr overrideCoveredAmount: Money

    //EISDEVTS-40103
    @Description("Override Covered CDT Code.")
    Attr overrideCoveredCdtCode: String

    //EISDEVTS-40103
    @Description("NOT USED")
    Attr overrideDeductible: Money

    //EISDEVTS-40103
    @Description("Override Eligibility Start Date.")
    Attr overrideEligibilityPeriod: Period

    //EISDEVTS-40103, EISDEVTS-47459
    @Description("Override Essential Health Benefit.")
    @Lookup("CapDNEssentialHealthBenefit")
    Attr overrideEssentialHealthBenefit: String

    //EISDEVTS-40103
    @Description("Override Fee Schedule.")
    Attr overrideFeeSchedule: String

    //EISDEVTS-40103
    @Description("Override Grace Period.")
    Attr overrideGracePeriod: Integer

    //EISDEVTS-40103
    @Description("Override Late Entrant Waiting Period.")
    Attr overrideLateEntrantWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("NOT USED")
    Attr overrideMaximumAmount: Money

    //EISDEVTS-40103
    @Description("Override Patient Responsibility.")
    Attr overridePatientResponsibility: Money

    //EISDEVTS-40103
    @Description("Override Payment Interest Amount.")
    Attr overridePaymentInterestAmount: Money

    //EISDEVTS-40103
    @Description("Override Payment Interest Number of Days.")
    Attr overridePaymentInterestDays: Integer

    //EISDEVTS-40103
    @Description("Override Payment Interest State.")
    Attr overridePaymentInterestState: String

    //EISDEVTS-40103
    @Description("Override Replacement Limit.")
    Attr overrideReplacementLimit: Integer

    //EISDEVTS-40103, EISDEVTS-39836
    @Description("Override Category of Service.")
    @Lookup("ServiceCategory")
    Attr overrideServiceCategory: String

    //EISDEVTS-40103
    @Description("Override Service Frequency Limit.")
    Attr overrideServiceFrequencyLimit: Integer

    //EISDEVTS-40103
    @Description("Override Service Category Waiting Period.")
    Attr overrideServiceWaitingPeriod: Integer

    //EISDEVTS-40103
    @Description("Override Student Dependent Status.")
    Attr overrideStudentDependentStatus: String
}
//EISDEVTS-39836
Entity CapDentalTermEntity is Term {

}
//EISDEVTS-39836
Entity CapDentalPolicyInfoInsuredDetailsEntity is CapInsuredInfo {

    //EISDEVTS-39870
    @Description("Insured's role.")
    @NonComparable
    Attr insuredRoleNameCd: String

    //EISDEVTS-39870
    @Description("Is patient full time student?")
    @NonComparable
    Attr isFullTimeStudent: Boolean

    //EISDEVTS-39870
    @Description("Patient's relationship to primary insured.")
    @NonComparable
    Attr relationshipToPrimaryInsuredCd: String

    @Description("Insured's hireDate.")
    @NonComparable
    Attr hireDate: Date
}
//EISDEVTS-39836
Entity CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity {

    //EISDEVTS-39870
    @Description("Late entrant basic Waiting Period.")
    @NonComparable
    Attr basicWaitingPeriod: Integer

    //EISDEVTS-39870
    @Description("Late entrant major Waiting Period.")
    @NonComparable
    Attr majorWaitingPeriod: Integer

    //EISDEVTS-39870
    @Description("Late entrant preventive Waiting Period.")
    @NonComparable
    Attr preventWaitingPeriod: Integer
}
//EISDEVTS-39836
Entity CapDentalPolicyInfoCoinsuranceEntity {

    //EISDEVTS-39870
    @Description("Coinsurance percentage in network.")
    @NonComparable
    Attr coinsuranceINPct: Decimal

    //EISDEVTS-39870
    @Description("Coinsurance percentage outside network.")
    @NonComparable
    Attr coinsuranceOONPct: Decimal

    //EISDEVTS-39870
    @Description("Coinsurance Service Type.")
    @NonComparable
    Attr coinsuranceServiceType: String
}
//EISDEVTS-47459
Entity CapDentalRemarkMessageEntity {

    //EISDEVTS-47459
    @Description("Additional multiple codes that explains how decision achieved.")
    @NonComparable
    Attr remarkCode: String

    //EISDEVTS-47459
    @Description("Messages as per the remark codes.")
    @NonComparable
    Attr remarkMessage: String
}

//EISDEVTS-48229
Entity CapDentalMaximumEntity {

    //GENESIS-184082
    @Description("Annual Maximum limit for Implants services.")
    @NonComparable
    Attr implantsINNAnnualMaximum: Money

    //EISDEVTS-40381
    @Description("Annual Maximum limit for Basic services.")
    @NonComparable
    Attr individualBasicINNAnnualMaximum: Money

    //EISDEVTS-40381
    @Description("Annual Maximum limit for Major services.")
    @NonComparable
    Attr individualMajorINNAnnualMaximum: Money

    //EISDEVTS-40381
    @Description("Annual Maximum limit for Preventive services.")
    @NonComparable
    Attr individualPreventiveINNAnnualMaximum: Money

    //GENESIS-184082
    @Description("Annual Maximum limit for Orthodontic services.")
    @NonComparable
    Attr orthoINNAnnualMaximum: Money
}
//EISDEVTS-48229
Entity CapDeductibleDetailEntity {

    //EISDEVTS-40381
    @Description("Annual Deductible limit for Basic services.")
    @NonComparable
    Attr individualBasicINNAnnualDeductible: Money

    //EISDEVTS-40381
    @Description("Annual Deductible limit for Major services.")
    @NonComparable
    Attr individualMajorINNAnnualDeductible: Money

    //EISDEVTS-40381
    @Description("Annual Deductible limit for Preventive services.")
    @NonComparable
    Attr individualPreventiveINNAnnualDeductible: Money
}

//GENESIS-353761
Entity CapPolicyFrequencyLimitationsEntity {

    //GENESIS-353761
    @Description("Preventive Limitations")
    Attr preventiveLimitations: CapPolicyPreventiveLimitationsEntity

    //GENESIS-353761
    @Description("Basic Limitations")
    Attr basicLimitations: CapPolicyBasicLimitationsEntity

    //GENESIS-353761
    @Description("Major Limitations")
    Attr majorLimitations: CapPolicyMajorLimitationsEntity
}

//GENESIS-353761
Entity CapPolicyPreventiveLimitationsEntity {

    //GENESIS-353761
    @Description("Oral Evaluations")
    Attr preventiveOralEvaluations: String

    //GENESIS-353754
    @Description("Fluoride Treatment")
    Attr preventiveFluorideTreatment: String

    //GENESIS-353754
    @Description("Fluoride Treatment Age Limit")
    Attr preventiveFluorideTreatmentAgeLimit: String

    //GENESIS-353754
    @Description("Bitewing Radiographs")
    Attr preventiveBitewingRadiographs: String
}

//GENESIS-353761
Entity CapPolicyBasicLimitationsEntity {

    //GENESIS-353761
    @Description("Periodontal Surgery")
    Attr basicPeriodontalSurgery: String

    //GENESIS-353754
    @Description("Stainless Steel Crowns")
    Attr basicStainlessSteelCrowns: String

    //GENESIS-353754
    @Description("Stainless Steel Crowns Age Limit")
    Attr basicStainlessSteelCrownsAgeLimit: String
}

//GENESIS-353761
Entity CapPolicyMajorLimitationsEntity {

    //GENESIS-353761
    @Description("Crowns")
    Attr majorCrowns: String

    //GENESIS-353761
    @Description("Implants")
    Attr majorImplants: String

    //GENESIS-353761
    @Description("Denture Adjustments")
    Attr majorDentureAdjustments: String
}

//GENESIS-353761
Entity CapPolicyServiceCategoryEntity {

    //GENESIS-353761
    @Description("Service Categories Oral Evaluations")
    Attr scOralEvaluations: String

    //GENESIS-353761
    @Description("Surgical Periodontics")
    Attr scSurgicalPeriodontics: String

    //GENESIS-353761
    @Description("Service Categories Crowns")
    Attr scCrowns: String

    //GENESIS-353761
    @Description("Implant Services")
    Attr scImplantServices: String

    //GENESIS-353454
    @Description("Fillings")
    Attr scFillings: String

    //GENESIS-353754
    @Description("Fluorides")
    Attr scFluorides: String

    //GENESIS-353754
    @Description("Stainless Steel Crowns")
    Attr scStainlessSteelCrowns: String

    //GENESIS-353754
    @Description("Bitewing Radiographs")
    Attr scBitewingRadiographs: String

    //GENESIS-353762
    @Description("Full Mouth Radiographs")
    Attr scFullMouthRadiographs: String

    //GENESIS-353762
    @Description("All Other Radiographs")
    Attr scAllOtherRadiographs: String

    //GENESIS-353762
    @Description("Root Canals")
    Attr scRootCanals: String
}

//GENESIS-354053
Entity CapDentalEntryPreviouslyUsedEntity {

    //GENESIS-354053
    @Description("Procedure ID")
    Attr procedureID: String

    //GENESIS-354053
    @Description("Deductible Used Amount")
    Attr deductibleUsedAmount: Money

    //GENESIS-354053
    @Description("Maximum Used Amount")
    Attr maximumUsedAmount: Money
}
