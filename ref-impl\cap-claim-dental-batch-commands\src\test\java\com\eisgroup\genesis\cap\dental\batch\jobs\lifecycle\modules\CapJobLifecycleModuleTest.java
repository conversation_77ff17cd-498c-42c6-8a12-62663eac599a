package com.eisgroup.genesis.cap.dental.batch.jobs.lifecycle.modules;

import com.eisgroup.genesis.cap.dental.batch.jobs.CapDentalPaymentGenerationJob;
import com.eisgroup.genesis.cap.dental.batch.jobs.CapDentalPaymentIssueJob;
import com.eisgroup.genesis.cap.dental.batch.lifecycle.modules.CapJobLifecycleModule;
import com.eisgroup.genesis.cap.jobs.reindex.CapReindexJob;
import com.eisgroup.genesis.cap.jobs.reindex.step.CapReindexJobStep;
import org.junit.Before;
import org.junit.Test;

import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapJobLifecycleModuleTest {

    private static final String MODEL_NAME = "capDentalFinancialJobs";

    private CapJobLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapJobLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalPaymentGenerationJob.class,
                CapDentalPaymentIssueJob.class,
                CapReindexJob.class,
                CapReindexJobStep.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

}
