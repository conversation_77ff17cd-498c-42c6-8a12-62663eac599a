{"requestId": "7f0c6745-334b-452f-a3a5-0486cbbaea4a", "body": {"response": "facadeLink", "success": {"_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3}, "_type": "IndividualCustomer", "_modelName": "INDIVIDUALCUSTOMER", "_modelVersion": "9", "_timestamp": "2025-08-12T09:06:02.195Z", "accessTrackInfo": {"_key": {"id": "ccb1dcd1-e31c-31a9-9ee2-40bdf459c5ab", "rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "parentId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}, "_type": "GenesisAccessTrackInfo", "createdBy": "qa", "createdOn": "2025-08-08T09:38:43.793Z", "updatedBy": "qa", "updatedOn": "2025-08-12T09:06:02.195Z"}, "productsOwned": [], "participationInfo": {"_type": "GenesisParticipationInfo", "employments": [{"originalHireDate": "2024-08-01", "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "dbd211cc-c79d-3f45-bef3-9bcef4a906f0", "parentId": "45ec2328-2ccb-3b35-bae6-689e4c703336"}, "_type": "EmploymentDetails", "customer": {"_type": "EmploymentDetailsCustomerAssociation", "link": {"_uri": "geroot://Customer/ORGANIZATIONCUSTOMER//be2ca36e-a34d-3749-b066-1678e4b4ef92"}, "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "8a1fad3c-a58c-3c44-8419-094f68d2e516", "parentId": "dbd211cc-c79d-3f45-bef3-9bcef4a906f0"}, "customerNumber": "OC0000019233"}}, {"originalHireDate": "2024-08-01", "_type": "EmploymentDetails", "customer": {"_type": "EmploymentDetailsCustomerAssociation", "link": {"_uri": "geroot://Customer/ORGANIZATIONCUSTOMER//fe3b3b1d-1b7a-48ed-9109-503e62c82e62"}, "customerNumber": "OC0000043644", "_key": {"id": "f02842e2-2121-3e1f-a668-f822727355be", "rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "parentId": "19daaf9a-a7bb-3a85-9364-fca7fe77746d"}}, "_key": {"id": "19daaf9a-a7bb-3a85-9364-fca7fe77746d", "rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "parentId": "45ec2328-2ccb-3b35-bae6-689e4c703336"}}], "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "45ec2328-2ccb-3b35-bae6-689e4c703336", "parentId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}}, "customerGroupInfos": [], "customerNumber": "IC0000066747", "communicationInfo": {"emails": [], "webAddresses": [], "addresses": [{"_type": "GenesisCrmAddress", "updatedOn": "2025-08-08T09:38:43.793Z", "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "3b4b8c1c-08c6-34cb-87cd-bd4d7c5f5145", "parentId": "29f13ac3-1fd3-34e1-96eb-33862aba7fca"}, "doNotSolicit": false, "location": {"city": "City", "stateProvinceCd": "MS", "addressType": "Mailing", "postalCode": "90001", "_type": "GenesisLocation", "countryCd": "US", "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "dcb35f05-1e0a-32ef-98a1-f21b8114baf6", "parentId": "3b4b8c1c-08c6-34cb-87cd-bd4d7c5f5145"}, "registryEntityNumber": "LO0000003158", "addressLine1": "Insured Address Line 1", "registryTypeId": "registry://Location/b149a4c6-a29d-3cd9-b78b-23143578dbde"}, "preferred": true}], "preferredContactMethod": "Address", "_type": "GenesisCrmCommunicationInfo", "chats": [], "phones": [], "socialNets": [], "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "29f13ac3-1fd3-34e1-96eb-33862aba7fca", "parentId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}}, "businessEntities": [], "paymentMethods": [{"_type": "CheckEntity", "preferred": true, "_key": {"id": "9955ed49-355d-3c6c-859b-6f3155eaf8f1", "rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "parentId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}, "effectiveDate": "2025-08-12", "expirationDate": "2025-08-12"}], "details": {"_type": "GenesisCrmIndividualDetails", "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "4010b112-3d03-3dbc-851b-961e38931609", "parentId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}, "person": {"lastName": "Red", "_type": "GenesisCrm<PERSON>erson", "_key": {"rootId": "fa5b5040-da0e-3c25-add3-5bc0f04eb34e", "revisionNo": 3, "id": "8baf5bbe-5a7e-33c4-b2b8-cebf82f05b76", "parentId": "4010b112-3d03-3dbc-851b-961e38931609"}, "birthDate": "1985-06-09", "registryEntityNumber": null, "firstName": "<PERSON><PERSON><PERSON>", "genderCd": "Male", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//fa5b5040-da0e-3c25-add3-5bc0f04eb34e"}, "tobaccoCd": "No"}, "additionalNames": [], "state": "customer", "taxExemptComment": ""}}, "finalResponse": true}