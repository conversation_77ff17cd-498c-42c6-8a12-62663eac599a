package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import org.junit.Test;

import java.lang.reflect.Constructor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class CapDentalSettlementCommandsTest {

    @Test
    public void testConstantsHaveCorrectValues() {
        assertEquals("updateSettlement", CapDentalSettlementCommands.UPDATE_SETTLEMENT);
    }

    @Test
    public void testConstructorIsPrivate() throws Exception {
        Constructor<CapDentalSettlementCommands> constructor = CapDentalSettlementCommands.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        assertNotNull(constructor.newInstance());
    }
}
