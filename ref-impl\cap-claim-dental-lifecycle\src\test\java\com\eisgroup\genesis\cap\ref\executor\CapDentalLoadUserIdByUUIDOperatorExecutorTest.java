/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.executor;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.eisgroup.genesis.transformation.model.operations.Operation;
import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.Callable;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link CapDentalLoadUserIdByUUIDOperatorExecutor}
 *
 * <AUTHOR>
 * @since 25.200
 */
public class CapDentalLoadUserIdByUUIDOperatorExecutorTest {

    private static final String NAME = "LoadUserIdByUUID";
    private CapDentalLoadUserIdByUUIDOperatorExecutor executor;
    private GenericOperation operation;
    @Mock
    private Operation userUUIDOp;
    @Mock
    private TransformationContext<JsonElement> context;
    @Mock
    private HttpClient httpClient;
    @Mock
    private ServiceAccessRunner serviceAccessRunner;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        executor = new CapDentalLoadUserIdByUUIDOperatorExecutor(httpClient, "http://localhost", serviceAccessRunner);
        operation = new GenericOperation(executor.getName(), List.of(userUUIDOp));
    }

    @Test
    public void shouldReturnName() {
        assertThat(executor.getName(), equalTo(NAME));
    }

    @Test
    public void showReturnLoadUserIdByUUIDExecutor() throws IOException {
        JsonPrimitive userUUID = new JsonPrimitive("fa5b5040-da0e-3c25-add3-5bc0f04eb34e");
        when(context.execute(userUUIDOp)).thenReturn(userUUID);
        when(httpClient.execute(any(HttpPost.class), any(JsonResponseHandler.class))).thenReturn(JsonUtils.loadJson("json/customer/INDIVIDUALCUSTOMER.json"));
        when(serviceAccessRunner.runLazy(any())).thenAnswer(a -> a.<Callable<Lazy<Object>>>getArgument(0).call());

        var value = executor.execute(operation, context);
        assertThat(value.isJsonPrimitive(), is(true));
        assertThat(value.getAsString(), equalTo(userUUID.getAsString()));
    }
}
