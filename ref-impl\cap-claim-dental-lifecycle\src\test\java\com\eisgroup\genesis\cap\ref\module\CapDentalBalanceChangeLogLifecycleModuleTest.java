package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.capdentalbalancechangelog.CapDentalBalanceChangeLogInitHandler;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalBalanceChangeLogLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalBalanceChangeLog";

    private static final String MODEL_TYPE = "CapBalanceChangeLog";

    private CapDentalBalanceChangeLogLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalBalanceChangeLogLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalBalanceChangeLogInitHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapTransformationCommandsConfig.class), equalTo(true));
    }
}
