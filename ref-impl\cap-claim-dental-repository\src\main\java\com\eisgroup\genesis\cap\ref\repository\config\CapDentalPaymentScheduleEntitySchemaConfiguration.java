/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.eisgroup.genesis.entity.metadata.config.EntitySchemaConfiguration;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;

/**
 * Entity schema configuration for Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleEntitySchemaConfiguration implements EntitySchemaConfiguration {

    private static final ModelRepository<DomainModel> MODEL_REPOSITORY = ModelRepositoryFactory
            .getRepositoryFor(DomainModel.class);

    private static final String CAP_PAYMENT_SCHEDULE = "CapPaymentSchedule";

    @Override
    public Collection<String> getAppliesTo() {
        return MODEL_REPOSITORY.getAllModels().stream().map(MODEL_REPOSITORY::getActiveModel)
                .filter(model -> CAP_PAYMENT_SCHEDULE.equals(model.getModelType()))
                .flatMap(model -> getSchemaName(model).stream())
                .collect(Collectors.toList());
    }

    @Override
    public Collection<Class<?>> getScanClassesNames() {
        return List.of();
    }

    private Collection<String> getSchemaName(DomainModel model) {
        return List.of(ModeledEntitySchemaResolver.getSchemaNameUsing(model, null));
    }
}
