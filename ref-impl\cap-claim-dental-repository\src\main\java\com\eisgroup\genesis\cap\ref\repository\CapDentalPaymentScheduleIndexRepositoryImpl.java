/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.ReadStatement;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.parameter.TransformationInput;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;

/**
 * Default {@link CapDentalPaymentScheduleIndexRepository} implementation.
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapDentalPaymentScheduleIndexRepositoryImpl implements CapDentalPaymentScheduleIndexRepository {

    private final ColumnStore columnStore;

    private final StatementBuilderFactory statementFactory;

    private final ModelRepository<TransformationModel> transformationRepository;

    private final ModeledTransformationService modeledTransformationService;

    private final String loadIndexTransformationName;

    public CapDentalPaymentScheduleIndexRepositoryImpl(ColumnStore columnStore, StatementBuilderFactory statementFactory,
                                                       ModelRepository<TransformationModel> transformationRepository,
                                                       ModeledTransformationService modeledTransformationService,
                                                       String loadIndexTransformationName) {
        this.columnStore = columnStore;
        this.statementFactory = statementFactory;
        this.transformationRepository = transformationRepository;
        this.modeledTransformationService = modeledTransformationService;
        this.loadIndexTransformationName = loadIndexTransformationName;
    }

    @Override
    public Streamable<List<CapDentalPaymentScheduleIdxEntity>> load(String applicableState) {

        ReadStatement<CapDentalPaymentScheduleIdxEntity> rs = statementFactory.read(CapDentalPaymentScheduleIdxEntity.class)
                .enableBatch()
                .joinAll()
                .build();

        return columnStore.execute("CapDentalPaymentSchedule", rs)
                .filter(capDentalPaymentScheduleIndexEntity -> applicableState.equals(capDentalPaymentScheduleIndexEntity.getState()))
                 .buffer(1000).map(s -> s.collect(Collectors.toList()))
                .filter(sourceIndexEntities -> sourceIndexEntities.iterator().hasNext());
    }

    @Override
    public Streamable<CapDentalPaymentScheduleIdxEntity> resolvePaymentScheduleIndex(
            EntityLink<RootEntity> originSource, List<String> applicableStates) {
        TransformationModel indexModel = transformationRepository.getActiveModel(loadIndexTransformationName);
        return modeledTransformationService.transformAll(indexModel, createTransformationInput(indexModel, originSource))
                .flatMap(transformationOutput -> Streamable.from(transformationOutput.asCollection())
                        .cast(CapDentalPaymentScheduleIdxEntity.class))
                .filter(index -> CollectionUtils.isEmpty(applicableStates) || applicableStates.contains(index.getState()));
    }

    private TransformationInput createTransformationInput(TransformationModel indexModel, EntityLink<RootEntity> originSource) {
        JsonObject originSourceKeyFilter = new JsonObject();
        originSourceKeyFilter.addProperty(CapDentalPaymentScheduleIdxEntity.ORIGIN_SOURCE_ATTR, originSource.getURIString());
        return TransformationInput.builder(indexModel).generic(originSourceKeyFilter).build();
    }
}