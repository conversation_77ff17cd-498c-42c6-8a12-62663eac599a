/**
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

export enum CustomerTypes {
    INDIVIDUALCUSTOMER = 'INDIVIDUALCUSTOMER',
    ORGANIZATIONCUSTOMER = 'ORGANIZATIONCUSTOMER'
}

export enum ProviderTypes {
    INDIVIDUALCUSTOMER = 'IndividualProvider',
    ORGANIZATIONCUSTOMER = 'OrganizationProvider'
}

export const CUSTOMER = 'Customer'
export const PROVIDER = 'Provider'

export const UUID_V4_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

export enum PayeeTypes {
    PrimaryInsured = 'PrimaryInsured',
    ServiceProvider = 'Provider',
    Alternate = 'AlternatePayee'
}

export enum TransactionType {
    ACTUAL_SERVICES = 'ActualServices',
    ORTHODONTIC_SERVICES = 'OrthodonticServices',
    PREDETERMINATION_ACTUAL_SERVICES = 'PredeterminationActualServices',
    PREDETERMINATION_ORTHODONTICS = 'PredeterminationOrthodontics'
}

export enum DrawerWidth {
    SUPERLARGE = 1080,
    LARGE = 808,
    SMALL = 632
}

export enum CreateOrUpdate {
    CREATE = 'create',
    UPDATE = 'update',
    VIEW = 'view'
}

export const SUBMITTED_PROCEDURES_URI_PATH = 'entity.lossDetail.submittedProcedures'
export const PROVIDER_ROLE_PATH = 'lossDetail.claimData.providerRole'

export enum POLICY_AND_PATIENT_STORE {
    INIT_STORE = 'INIT_STORE',
    LOAD_POLICY = 'LOAD_POLICY',
    LOAD_INSUREDS = 'LOAD_INSUREDS',
    LOAD_DENTAL_POLICY = 'LOAD_DENTAL_POLICY',
    LOAD_DENTAL_MASTER_POLICY = 'LOAD_DENTAL_MASTER_POLICY',
    LOAD_GROUP_SPONSOR = 'LOAD_GROUP_SPONSOR',
    LOAD_MASTER_POLICY_LIST = 'LOAD_MASTER_POLICY_LIST'
}

export enum LOSS_SUBMIT_STATUS {
    PENDING = 'PENDING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED'
}

export const DOCUMENT_DATE_FORMAT = 'YYYYMMDD_HHmmss'

export const MAX_SINGLE_FILE_SIZE = ********

export const MASK_CHAR = '.'
export const ACCOUNT_MASK = '···· ···· ····'

export enum DocumentError {
    TYPE_ERROR = 1,
    SIZE_ERROR = 2
}

export const getDocumentErrorMessage = (typeCode: number): string => {
    const type = {
        1: 'dental-product:upload_documents_file_type_alert',
        2: 'dental-product:upload_documents_file_max_size_alert'
    }
    return type[typeCode] || typeCode
}

// TODO Mock provider NPI, will be removed in next sprint
export const INNNetworkCodes = ['**********', '**********']
