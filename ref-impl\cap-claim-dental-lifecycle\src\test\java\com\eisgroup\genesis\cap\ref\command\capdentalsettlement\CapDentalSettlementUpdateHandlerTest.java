/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementUpdateInput;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import com.eisgroup.genesis.factory.services.AccessTrackInfoService;
import com.eisgroup.genesis.gson.GsonFactory;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalSettlementUpdateHandlerTest {

    @InjectMocks
    private CapDentalSettlementUpdateHandler handler;

    @Mock
    private AccessTrackInfoService accessTrackInfoService;

    @Mock
    private ModelResolver modelResolver;

    @Before
    public void setUp() {
        ModelRepository<DomainModel> repo = ModelRepositoryFactory.getRepositoryFor(DomainModel.class);
        Mockito.when(modelResolver.resolveModel(DomainModel.class)).thenAnswer(args -> repo.getActiveModel("CapDentalSettlement"));
    }

    @Test
    public void testHandlerExecute() throws IOException {
        //given
        JsonObject updateDentalSettlement = JsonUtils.loadJson("json/capDentalSettlement/updateDentalSettlementInput.json");
        CapDentalSettlementUpdateInput request = new CapDentalSettlementUpdateInput(updateDentalSettlement);

        JsonObject dentalSettlement = JsonUtils.loadJson("json/capDentalSettlement/DentalSettlement.json");
        CapSettlement capSettlement = GsonFactory.gson().fromJson(dentalSettlement, CapSettlement.class);

        assertThat(
                //when
                handler.execute(request, capSettlement),
                //then
                is(not(nullValue())));
    }

    @Test
    public void testReturnHandlerName() {
        assertThat(handler.getName(), equalTo("updateSettlement"));
    }

}
