/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {render} from '@testing-library/react'
import {describe, expect, it, vi} from 'vitest'

import {
    renderDateOfService,
    renderDiagnosisCode,
    renderDiagnosisQualifier,
    renderProcedureCode,
    renderQuantity,
    renderSubmittedFee
} from '../../../../src/shared/common/claim-procedures/column-renderers'
import {wrapInForm} from '../../../support/TestUtils'

// Mock external dependencies
vi.mock('@eisgroup/i18n', async importOriginal => {
    const actual = (await importOriginal()) as any
    return {
        ...actual,
        t: (key: string) => {
            const translations = {
                'dental-product:empty': 'Empty',
                'dental-product:dental-claim-claim-details-services-date-of-service': 'Date of Service',
                'dental-product:dental-claim-claim-details-services-procedure-code': 'Procedure Code',
                'dental-product:dental-claim-claim-details-services-quantity': 'Quantity',
                'dental-product:dental-claim-claim-details-services-charges': 'Charges',
                'dental-product:dental-claim-edit-procedure-details-diagnosis-qualifier': 'Diagnosis Qualifier',
                'dental-product:dental-claim-edit-procedure-details-diagnosis-code': 'Diagnosis Code',
                'dental-product:field_mandatory_message': 'Field is mandatory'
            }
            return translations[key] || key
        },
        LocalizationUtils: {
            dateValue: (date: Date) => ({
                toString: () => date.toLocaleDateString()
            })
        }
    }
})

vi.mock('../../../utils/common/utils', () => ({
    getDateFormattingByLocale: () => ({
        format: 'MM/DD/YYYY'
    })
}))

vi.mock('../../../utils/helpers', () => ({
    moneyByLocale: (amount: number) => `$${amount.toFixed(2)}`
}))

vi.mock('../../../utils/common/ValidationUtils', () => ({
    required: vi.fn(() => vi.fn())
}))

vi.mock('./validators', () => ({
    createValidators: {
        dateOfService: () => vi.fn(),
        procedureCode: () => vi.fn(),
        quantity: () => vi.fn(),
        submittedFee: () => vi.fn(),
        diagnosisQualifier: () => vi.fn(),
        diagnosisCode: () => vi.fn()
    }
}))

vi.mock('./classnames', () => ({
    CLAIM_PROCEDURE_FORM_ITEM_ROW: 'claim-procedure-form-item-row',
    DIAGNOSIS_TABLE_ITEM_ROW: 'diagnosis-table-item-row'
}))

vi.mock('../../components/procedure-code-search/ProcedureCodeSearchWithFormField', () => ({
    ProcedureCodeSearchWithFormField: ({name}) => <div data-testid='procedure-code-search'>{name}</div>
}))

describe('column-renderers', () => {
    const mockEntity = {
        dateOfService: new Date('2023-01-15'),
        procedureCode: 'D0120',
        quantity: 1,
        submittedFee: {
            amount: 150.0,
            currency: 'USD'
        },
        diagnosisCodes: [],
        surfaces: [],
        toothCodes: [],
        _type: 'CapDentalProcedureEntity' as const,
        _key: {value: 'test-key'} as any
    }

    const mockDiagnosisEntity = {
        qualifier: 'ICD10',
        code: 'K02.9',
        _type: 'CapDentalDiagnosisCodeEntity' as const,
        _key: {value: 'test-diagnosis-key'} as any
    }

    const mockPrefixName = 'test.prefix'

    const mockProcedureCodeList = [
        {code: 'D0120', displayValue: 'Periodic oral evaluation'},
        {code: 'D1110', displayValue: 'Adult prophylaxis'}
    ]

    const mockQualifierOptions = [
        {code: 'ICD10', displayValue: 'ICD-10-CM'},
        {code: 'ICD9', displayValue: 'ICD-9-CM'}
    ]

    describe('renderDateOfService', () => {
        it('should render text field when not editable', () => {
            const result = renderDateOfService(mockEntity, false, mockPrefixName, true)
            const {container} = render(wrapInForm(result as React.ReactElement))

            expect(container).toBeDefined()
        })

        it('should render datepicker when editable', () => {
            const result = renderDateOfService(mockEntity, true, mockPrefixName, true)
            const {container} = render(wrapInForm(result as React.ReactElement))

            expect(container.querySelector('[name="test.prefix.dateOfService"]')).toBeTruthy()
        })

        it('should show required validation when dateOfServiceRequired is true', () => {
            const result = renderDateOfService(mockEntity, true, mockPrefixName, true)
            const {container} = render(wrapInForm(result as React.ReactElement))

            const input = container.querySelector('[name="test.prefix.dateOfService"]')
            expect(input).toBeTruthy()
        })
    })

    describe('renderProcedureCode', () => {
        it('should render display value when not editable', () => {
            const result = renderProcedureCode(mockEntity, false, mockPrefixName, mockProcedureCodeList)

            expect(result).toBe('Periodic oral evaluation')
        })

        it('should render procedure code search when editable', () => {
            const result = renderProcedureCode(mockEntity, true, mockPrefixName, mockProcedureCodeList)
            const {getByTestId} = render(wrapInForm(result as React.ReactElement))

            expect(getByTestId('procedure-code-search')).toBeTruthy()
        })
    })

    describe('renderQuantity', () => {
        it('should render entity quantity when not editable', () => {
            const result = renderQuantity(mockEntity, false, mockPrefixName)

            expect(result).toBe(1)
        })

        it('should render input number when editable', () => {
            const result = renderQuantity(mockEntity, true, mockPrefixName)
            const {container} = render(wrapInForm(result as React.ReactElement))

            const input = container.querySelector('[name="test.prefix.quantity"]')
            expect(input).toBeTruthy()
        })
    })

    describe('renderSubmittedFee', () => {
        it('should render formatted money when not editable and has amount', () => {
            const result = renderSubmittedFee(mockEntity, false, mockPrefixName)

            expect(result).toBe('$150.00')
        })

        it('should render Empty when not editable and no amount', () => {
            const entityWithoutFee = {...mockEntity, submittedFee: undefined}
            const result = renderSubmittedFee(entityWithoutFee, false, mockPrefixName)

            expect(result).toBe('Empty')
        })

        it('should render money input when editable', () => {
            const result = renderSubmittedFee(mockEntity, true, mockPrefixName)
            const {container} = render(wrapInForm(result as React.ReactElement))

            const input = container.querySelector('[name="test.prefix.submittedFee"]')
            expect(input).toBeTruthy()
        })
    })

    describe('renderDiagnosisQualifier', () => {
        it('should render display value when not editable', () => {
            const result = renderDiagnosisQualifier(mockDiagnosisEntity, false, mockPrefixName, mockQualifierOptions)

            expect(result).toBe('ICD-10-CM')
        })

        it('should render lookup select when editable', () => {
            const result = renderDiagnosisQualifier(mockDiagnosisEntity, true, mockPrefixName, mockQualifierOptions)
            const {container} = render(wrapInForm(result as React.ReactElement))

            const select = container.querySelector('[name="test.prefix.qualifier"]')
            expect(select).toBeTruthy()
        })
    })

    describe('renderDiagnosisCode', () => {
        it('should render entity code when not editable', () => {
            const result = renderDiagnosisCode(mockDiagnosisEntity, false, mockPrefixName)

            expect(result).toBe('K02.9')
        })

        it('should render input when editable', () => {
            const result = renderDiagnosisCode(mockDiagnosisEntity, true, mockPrefixName)
            const {container} = render(wrapInForm(result as React.ReactElement))

            const input = container.querySelector('[name="test.prefix.code"]')
            expect(input).toBeTruthy()
        })
    })
})
