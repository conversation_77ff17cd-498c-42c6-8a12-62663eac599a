<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:design="http://flowable.org/design" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://flowable.org/test" design:palette="flowable-work-process-palette">
  <process id="dentalProcessPaymentHubEvent" name="dentalProcessPaymentHubEvent" isExecutable="true">
    <extensionElements>
      <design:stencilid><![CDATA[BPMNDiagram]]></design:stencilid>
      <design:creationdate><![CDATA[2025-08-01T02:02:27.722Z]]></design:creationdate>
      <design:modificationdate><![CDATA[2025-08-01T02:51:51.921Z]]></design:modificationdate>
    </extensionElements>
    <serviceTask id="sid-FCFE0110-4AF8-4ACC-B9F7-846B0578907E" name="Load Payment" flowable:async="true" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceGroup('CapDentalPaymentDefinition').serviceName('factory-CapPayment').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{"body": {"links": [ {"_uri": "${processPayload.paymentUri}"}]}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="disallowRedirects">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[payments]]></flowable:string>
        </flowable:field>
        <flowable:field name="ignoreException">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveRequestVariables">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParameters">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParametersTransient">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
        <flowable:executionListener event="end" expression="${execution.setVariable('payment', payments.body.success.elements().next())}"></flowable:executionListener>
        <flowable:executionListener event="end" expression="${execution.setVariable('_key', payments.body.success.elements().next()._key)}"></flowable:executionListener>
        <design:stencilid><![CDATA[HttpTask]]></design:stencilid>
        <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
      </extensionElements>
    </serviceTask>
    <serviceTask id="sid-DA646323-D943-498A-B39D-69789E2B5D1C" name="Issue Payment" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter source="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter source="{&quot;_key&quot; : ${payment._key}}" target="payload"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="issuePayment" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter source="payment" target="_variation"></flowable:eventInParameter>
        <flowable:channelKey><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter name="requestId" value="${requestId}"></flowable:triggerEventCorrelationParameter>
        <design:stencilid><![CDATA[SendAndReceiveEventTask]]></design:stencilid>
        <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
        <design:sendsynchronously><![CDATA[false]]></design:sendsynchronously>
      </extensionElements>
    </serviceTask>
    <exclusiveGateway id="sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40" name="What's the payment event?">
      <extensionElements>
        <design:stencilid><![CDATA[Exclusive_Databased_Gateway]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
    </exclusiveGateway>
    <startEvent id="processPaymentLifecycleEntryPoint" name="Process Payment Lifecycle" isInterrupting="true">
      <extensionElements>
        <flowable:executionListener event="end" expression="${execution.setVariable('_uri', processPayload.paymentUri)}"></flowable:executionListener>
        <flowable:executionListener event="end" expression="${flwStringUtils.hasText(processPayload.paymentUri) ? processEngine.getRuntimeService().updateBusinessKey(execution.getProcessInstanceId(), processPayload.paymentUri) : null}"></flowable:executionListener>
        <flowable:eventType><![CDATA[CapDentalPaymentDefinition_processPaymentLifecycle]]></flowable:eventType>
        <flowable:eventOutParameter source="payload" target="processPayload"></flowable:eventOutParameter>
        <flowable:channelKey><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
        <flowable:work-form-field-validation><![CDATA[false]]></flowable:work-form-field-validation>
        <design:stencilid><![CDATA[StartEventRegistryEvent]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
    </startEvent>
    <endEvent id="sid-D7707E89-60A9-42B6-86E5-0D4D778DD2B5">
      <extensionElements>
        <design:stencilid><![CDATA[EndNoneEvent]]></design:stencilid>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="sid-C6AEF3A6-BA7D-4237-8A3B-2ADA62C25063" sourceRef="sid-FCFE0110-4AF8-4ACC-B9F7-846B0578907E" targetRef="sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="sid-318D9365-829A-41A2-9E13-3F4D603EAD38" sourceRef="sid-DA646323-D943-498A-B39D-69789E2B5D1C" targetRef="sid-D7707E89-60A9-42B6-86E5-0D4D778DD2B5">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="sid-DC946D86-4D56-4788-96B7-3F502E5419F4" sourceRef="processPaymentLifecycleEntryPoint" targetRef="sid-FCFE0110-4AF8-4ACC-B9F7-846B0578907E">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="sid-C64DDC77-B276-8A05-AF9D-FD3AEC912871" sourceRef="sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40" targetRef="sid-D7707E89-60A9-42B6-86E5-0D4D778DD2B5">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${processPayload.paymentEvent != "OutboundPaymentCreatedEvent"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-51ACB6FE-D61B-2405-C209-25F63E711144" name="Create Event" sourceRef="sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40" targetRef="sid-DA646323-D943-498A-B39D-69789E2B5D1C">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${processPayload.paymentEvent == "OutboundPaymentCreatedEvent"}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_dentalProcessPaymentHubEvent">
    <bpmndi:BPMNPlane bpmnElement="dentalProcessPaymentHubEvent" id="BPMNPlane_dentalProcessPaymentHubEvent">
      <bpmndi:BPMNShape bpmnElement="sid-FCFE0110-4AF8-4ACC-B9F7-846B0578907E" id="BPMNShape_sid-FCFE0110-4AF8-4ACC-B9F7-846B0578907E">
        <omgdc:Bounds height="80.0" width="100.0" x="453.0" y="224.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DA646323-D943-498A-B39D-69789E2B5D1C" id="BPMNShape_sid-DA646323-D943-498A-B39D-69789E2B5D1C">
        <omgdc:Bounds height="80.0" width="100.0" x="801.0" y="224.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40" id="BPMNShape_sid-CC933E36-D514-09CA-D9B4-3C1BB3760C40">
        <omgdc:Bounds height="40.0" width="40.0" x="663.0" y="244.0"></omgdc:Bounds>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="600.5" y="288.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="processPaymentLifecycleEntryPoint" id="BPMNShape_processPaymentLifecycleEntryPoint">
        <omgdc:Bounds height="30.0" width="30.0" x="276.0" y="249.0"></omgdc:Bounds>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="147.0" x="219.0" y="294.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-D7707E89-60A9-42B6-86E5-0D4D778DD2B5" id="BPMNShape_sid-D7707E89-60A9-42B6-86E5-0D4D778DD2B5">
        <omgdc:Bounds height="28.0" width="28.0" x="1010.0" y="250.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-DC946D86-4D56-4788-96B7-3F502E5419F4" id="BPMNEdge_sid-DC946D86-4D56-4788-96B7-3F502E5419F4" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="306.0" y="264.0"></omgdi:waypoint>
        <omgdi:waypoint x="453.0" y="264.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C6AEF3A6-BA7D-4237-8A3B-2ADA62C25063" id="BPMNEdge_sid-C6AEF3A6-BA7D-4237-8A3B-2ADA62C25063" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="553.0" y="264.0"></omgdi:waypoint>
        <omgdi:waypoint x="663.0" y="264.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-51ACB6FE-D61B-2405-C209-25F63E711144" id="BPMNEdge_sid-51ACB6FE-D61B-2405-C209-25F63E711144" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="703.0" y="264.0"></omgdi:waypoint>
        <omgdi:waypoint x="801.0" y="264.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="70.0" x="713.0" y="234.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-318D9365-829A-41A2-9E13-3F4D603EAD38" id="BPMNEdge_sid-318D9365-829A-41A2-9E13-3F4D603EAD38" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="901.0" y="264.0"></omgdi:waypoint>
        <omgdi:waypoint x="1010.0" y="264.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C64DDC77-B276-8A05-AF9D-FD3AEC912871" id="BPMNEdge_sid-C64DDC77-B276-8A05-AF9D-FD3AEC912871" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="683.0" y="244.0"></omgdi:waypoint>
        <omgdi:waypoint x="683.0" y="156.0"></omgdi:waypoint>
        <omgdi:waypoint x="1024.0" y="156.0"></omgdi:waypoint>
        <omgdi:waypoint x="1024.0" y="250.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
