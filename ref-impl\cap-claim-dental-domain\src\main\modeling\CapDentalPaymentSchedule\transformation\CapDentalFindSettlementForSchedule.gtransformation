@CapEndpoint("capDentalFindSettlementForSchedule")
Transformation CapDentalFindSettlementForSchedule {
  Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        ?  //as out
    }
    Var loadedLoss is ExtLink(schedule.originSource)
    Var settlementIndex is Load(createLossId(schedule), "CapDentalSettlementIndex", "CapDentalSettlementIdx")
    Var loadedSettlements is loadSettlement(settlementIndex.settlementId)
    Var approvedSettlements is FlatMap(loadedSettlements[filterApproved])
    Var currentSettlement is First(approvedSettlements<revisionNoDesc>).settlement

    Attr loss is loadedLoss
    Attr lossUri is ToExtLink(loadedLoss)
    Attr settlement is currentSettlement

    Producer createLossId(schedule) {
        Attr lossId is schedule.originSource._uri
    }

    Producer loadSettlement(settlementId){
        Var settlement is ExtLink(AsExtLink(settlementId))
        Attr settlement is settlement
        Attr revisionNo is settlement._key.revisionNo
    }

    Filter filterApproved {
        settlement.state == "Approved"
    }

    Sort revisionNoDesc {
        "revisionNo" -> "DESC"
    }
}