<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-lifecycle</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-repository</artifactId>
        </dependency>

        <!-- Event consumer resolver dependencies-->
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-resolver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-base-lifecycle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>data-override-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.confidential-data</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.search</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.relationships</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.unique-fields</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.query-fields</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-bundle.state-machine</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>search-index-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.entity-lock</artifactId>
            <type>tile</type>
        </dependency>

        <!-- Libraries -->
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>column-store-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <classifier>producer</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <type>tile</type>
            <classifier>consumer</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-stream-consumer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-stream-producer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.loss</groupId>
            <artifactId>cap-loss-base-lifecycle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.adjudication</groupId>
            <artifactId>cap-adjudication-base-lifecycle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-base-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-rules-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.kraken.command.integration</groupId>
            <artifactId>rules.integration-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-lifecycle-impl</artifactId>
        </dependency>
        <!-- Lookups -->
        <dependency>
            <groupId>com.eisgroup.genesis.lookups</groupId>
            <artifactId>lookups-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <!-- Versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>versioning-repository-api</artifactId>
        </dependency>

        <!-- Transformations -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>transformation-lifecycle-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-transformation-impl</artifactId>
        </dependency>


        <!--- Testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.utils</groupId>
            <artifactId>testing-utils</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>
</project>
