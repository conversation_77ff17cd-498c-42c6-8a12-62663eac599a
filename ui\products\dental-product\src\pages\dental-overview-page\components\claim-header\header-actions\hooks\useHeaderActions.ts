import {CapDentalLoss} from '@eisgroup/dental-models'
import {useTranslate} from '@eisgroup/i18n'

import {DropdownActions} from '../../../../../../shared/components'
import {hasAuthorities} from '../../../../../../utils'
import {redirectToClaimAdjustPage} from '../../../../../../utils/common/routing'
import {DentalClaimState} from '../../../../../../utils/types/enums'
import {DentalHeaderActionDrawerKey} from '../../ClaimHeader'
import {ClaimActions, stateAvailableActions} from '../constant'

export const useHeaderActions = (loss: CapDentalLoss.CapDentalLossEntity, openDrawer: (drawerKey: string) => void) => {
    const {t} = useTranslate()

    const getActionItems = (): DropdownActions<string> => {
        const availableActions = loss.state ? stateAvailableActions[loss.state as DentalClaimState] ?? [] : []
        return availableActions?.reduce((acc, action) => {
            const hasPrivileges = action.privileges.length ? hasAuthorities(action.privileges) : true
            return {
                ...acc,
                [action.action]: {
                    label: t(
                        `dental-product:dental-header-claim-action-${action.action.replaceAll('_', '-').toLowerCase()}`
                    ),
                    isHidden: !hasPrivileges
                }
            }
        }, {})
    }

    const onSelectAction = (action: ClaimActions): void => {
        switch (action) {
            case ClaimActions.ADJUST_CLAIM:
                redirectToClaimAdjustPage(loss._key.rootId, loss._key.revisionNo)
                break
            case ClaimActions.CHANGE_CLAIM_SUBSTATUS:
                openDrawer(DentalHeaderActionDrawerKey.CHANGE_SUBSTATUS_DRAWER_KEY)
                break
            case ClaimActions.SUSPEND_CLAIM:
                openDrawer(DentalHeaderActionDrawerKey.CONFIRM_SUSPEND_CLAIM_MODAL_KEY)
                break
            case ClaimActions.RECOVERY_CLAIM:
                openDrawer(DentalHeaderActionDrawerKey.CONFIRM_RECOVERY_CLAIM_MODAL_KEY)
                break
        }
    }

    return {
        getActionItems,
        onSelectAction
    }
}
