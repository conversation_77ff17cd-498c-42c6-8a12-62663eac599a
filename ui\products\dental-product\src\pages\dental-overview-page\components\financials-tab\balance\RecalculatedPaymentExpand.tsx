import React, {<PERSON>} from 'react'
import {sumBy} from 'lodash'

import {MoneyFormat} from '@eisgroup/dental-core'
import {CapDentalBalance, CapDentalSettlement} from '@eisgroup/dental-models'
import {useLookupValues} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'
import {Col, Row} from '@eisgroup/ui-kit'

import {formatDate} from '../../../../../utils/helpers'
import {EntityLink} from '../../../../../utils/helpers/entities'
import {PAYMENT_EXPAND_ROW_VALUE} from '../utils'
import {
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_PAYMENT_AMOUNT,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ROW,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_SHOULD_HAVE_PAID
} from './classnames'

import CapDentalBalanceItemEntity = CapDentalBalance.CapDentalBalanceItemEntity
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity

interface RecalculatedPaymentExpandProps {
    balanceItem: CapDentalBalanceItemEntity
    settlements: CapDentalSettlementEntity[]
}

export const RecalculatedPaymentExpand: FC<RecalculatedPaymentExpandProps> = props => {
    const {balanceItem, settlements} = props
    const {actualAllocations} = balanceItem
    const {values: procedureCodeList} = useLookupValues('CapDNProcedureCode', true)
    const {t} = useTranslate()

    const expandData = actualAllocations.map(allocation => {
        const currentAllocationSource = allocation.allocationSource?._uri
            ? EntityLink.from(allocation.allocationSource?._uri)
            : undefined
        const currentAllocationSourceRootId = currentAllocationSource?.rootId
        const currentAllocationSourceRevision = currentAllocationSource?.revisionNo
        const currentAllocationprocedureID = allocation.allocationPayableItem?.procedureID
        const settlement = settlements.find(
            settlement =>
                settlement._key.rootId === currentAllocationSourceRootId &&
                settlement._key.revisionNo.toString() === currentAllocationSourceRevision
        )
        const submittedProcedures = settlement?.settlementLossInfo?.submittedProcedures
        const settlementResultEntry = settlement?.settlementResult?.entries?.find(
            entry => entry.serviceSource === currentAllocationprocedureID
        )
        const submitPrecedure = submittedProcedures?.find(
            procedure => procedure._key.id === settlementResultEntry?.serviceSource
        )
        const procedureCode = settlementResultEntry?.status?.submittedCode ?? ''
        const procedureDesc = procedureCodeList?.find(one => one.code === procedureCode)?.displayValue ?? ''
        const dateOfService = submitPrecedure?.dateOfService ? formatDate(submitPrecedure?.dateOfService) : ''

        const serviceDetailTitle = `${procedureCode}-${procedureDesc}(${dateOfService})`
        const paymentTotalAllocation = allocation?.allocationGrossAmount?.amount
        const shouldHavePaidTotalAllocation =
            sumBy(allocation?.scheduledAllocations, schedule => schedule?.allocationGrossAmount!.amount) || 0

        return {
            key: allocation._key.rootId,
            serviceDetailTitle,
            paymentTotalAllocation,
            shouldHavePaidTotalAllocation
        }
    })

    // Calculate actual interest amount from actualAllocations
    const calculateActualInterest = (allocation: any) => {
        return sumBy(
            allocation?.allocationAdditions?.filter(
                (addition: any) => addition.additionType === PAYMENT_EXPAND_ROW_VALUE.INTEREST
            ) ?? [],
            (addition: any) => addition.appliedAmount?.amount ?? 0
        )
    }

    // Calculate scheduled interest amount from scheduledAllocations
    const calculateScheduledInterest = (allocation: any) => {
        return sumBy(
            allocation?.scheduledAllocations?.flatMap(
                (scheduled: any) =>
                    scheduled?.allocationAdditions?.filter(
                        (addition: any) => addition.additionType === PAYMENT_EXPAND_ROW_VALUE.INTEREST
                    ) ?? []
            ) ?? [],
            (addition: any) => addition.appliedAmount?.amount ?? 0
        )
    }
    return (
        <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND}>
            {expandData.map((data, index) => {
                const {key, serviceDetailTitle, paymentTotalAllocation, shouldHavePaidTotalAllocation} = data
                const currentAllocation = actualAllocations[index]

                // Calculate interest amounts for current allocation
                const actualInterestAmount = calculateActualInterest(currentAllocation)
                console.info('actualInterestAmount', actualInterestAmount)
                const scheduledInterestAmount = calculateScheduledInterest(currentAllocation)
                console.info('scheduledInterestAmount', scheduledInterestAmount)
                return (
                    <Row className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ROW} key={key}>
                        <Col span={8}>
                            <h4>{serviceDetailTitle}</h4>
                        </Col>
                        <Col span={8} className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_PAYMENT_AMOUNT}>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-allocation'
                                    )}
                                </label>
                                <MoneyFormat value={paymentTotalAllocation} />
                            </div>
                            {!!actualInterestAmount && (
                                <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST}>
                                    <label>
                                        {t(
                                            'dental-product:dental-claim-balance-balance-recalculated-payments-total-interests'
                                        )}
                                    </label>
                                    <MoneyFormat value={actualInterestAmount} />
                                </div>
                            )}
                        </Col>
                        <Col span={8} className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_SHOULD_HAVE_PAID}>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-allocation'
                                    )}
                                </label>
                                <MoneyFormat value={shouldHavePaidTotalAllocation} />
                            </div>
                            {!!scheduledInterestAmount && (
                                <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST}>
                                    <label>
                                        {t(
                                            'dental-product:dental-claim-balance-balance-recalculated-payments-total-interests'
                                        )}
                                    </label>
                                    <MoneyFormat value={scheduledInterestAmount} />
                                </div>
                            )}
                        </Col>
                    </Row>
                )
            })}
        </div>
    )
}
