/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.handler;

import com.eisgroup.genesis.cap.ref.events.CapDentalCaseInstanceReactivation;
import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalLossReactiveCaseInstanceEventHandler;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.streams.CommandExecutedMessageMetadata;
import com.eisgroup.genesis.streams.MessageMetadata;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CapDentalLossReactiveCaseInstanceEventHandlerTest {

    private CapDentalLossReactiveCaseInstanceEventHandler caseInstanceEventHandler;
    @Mock
    private EntityLinkBuilderRegistry linkBuilderRegistry;
    @Mock
    private CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation;

    @Before
    public void setUp() {
        caseInstanceEventHandler = new CapDentalLossReactiveCaseInstanceEventHandler(linkBuilderRegistry, capDentalCaseInstanceReactivation);
    }

    @Test
    public void testDentalLossReactiveSupports() {
        CommandExecutedMessageMetadata message = mock(CommandExecutedMessageMetadata.class);
        when(message.getCommandName()).thenReturn("reopenLoss");
        when(message.getModelName()).thenReturn("CapDentalLoss");

        //when
        boolean supports = caseInstanceEventHandler.supports(message);

        //then
        assertThat(supports, equalTo(true));
    }
}
