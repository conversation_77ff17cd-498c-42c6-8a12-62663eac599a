$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/services/dental-product-services[39m

 [32m✓[39m test/impl/utils.test.ts [2m([22m[2m2 tests[22m[2m)[22m[90m 3[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m   Start at [22m 15:01:43
[2m   Duration [22m 2.79s[2m (transform 70ms, setup 1.25s, collect 520ms, tests 3ms, environment 524ms, prepare 130ms)[22m

