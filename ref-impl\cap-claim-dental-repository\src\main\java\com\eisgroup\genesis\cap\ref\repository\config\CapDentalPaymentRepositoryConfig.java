/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.cap.ref.repository.CapDentalFinancialEntitySchemaConfiguration;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentIndexRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentIndexRepositoryImpl;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementVersionEntitySchemaConfiguration;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.entity.metadata.config.EntitySchemaConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CapDentalPaymentRepositoryConfig {

    @Bean
    public EntitySchemaConfiguration capFinancialEntitySchemaConfiguration() {
        return new CapDentalFinancialEntitySchemaConfiguration();
    }

    @Bean
    public EntitySchemaConfiguration capPaymentScheduleEntitySchemaConfiguration() {
        return new CapDentalPaymentScheduleEntitySchemaConfiguration();
    }

    @Bean
    public EntitySchemaConfiguration claimSettlementEntitySchemaConfiguration() {
        return new CapDentalSettlementVersionEntitySchemaConfiguration();
    }

    @Bean
    public CapDentalPaymentIndexRepository capPaymentDefinitionIndexRepository(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        return new CapDentalPaymentIndexRepositoryImpl(columnStore, statementFactory);
    }
}
