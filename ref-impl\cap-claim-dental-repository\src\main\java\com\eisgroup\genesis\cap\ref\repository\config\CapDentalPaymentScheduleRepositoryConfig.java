/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepositoryImpl;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.context.annotation.Bean;

/**
 * Configuration for payment job
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapDentalPaymentScheduleRepositoryConfig {

    @Bean
    public CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexResolver(ColumnStore columnStore,
                                                                                         StatementBuilderFactory statementFactory,
                                                                                         ModeledTransformationService modeledTransformationService) {
        return new CapDentalPaymentScheduleIndexRepositoryImpl(columnStore, statementFactory,
                ModelRepositoryFactory.getRepositoryFor(TransformationModel.class),
                modeledTransformationService, "CapLoadDentalPaymentSchedulesIndex");
    }
}