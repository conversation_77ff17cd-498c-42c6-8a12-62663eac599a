{"deductibleDetails": [{"individualPreventiveINNAnnualDeductible": {"currency": "USD", "amount": 100}, "individualBasicINNAnnualDeductible": {"currency": "USD", "amount": 100}, "individualMajorINNAnnualDeductible": {"currency": "USD", "amount": 100}, "_type": "CapDeductibleDetailEntity"}], "dentalMaximums": [{"individualPreventiveINNAnnualMaximum": {"currency": "USD", "amount": 100}, "individualBasicINNAnnualMaximum": {"currency": "USD", "amount": 100}, "individualMajorINNAnnualMaximum": {"currency": "USD", "amount": 100}, "orthoINNAnnualMaximum": {"amount": 100, "currency": "USD"}, "implantsINNAnnualMaximum": {"amount": 100, "currency": "USD"}, "_type": "CapDentalMaximumEntity"}], "term": {"effectiveDate": "2020-10-27T13:44:24.154Z", "expirationDate": "2022-10-27T13:44:24.154Z", "_type": "Term"}, "policyPaidToDate": "2022-10-27", "productCd": "DNIndividual", "policyPaidToDateWithGracePeriod": "2022-10-27", "plan": "High", "planCategory": "PPO", "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 99, "coinsuranceOONPct": 97, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}, {"coinsuranceServiceType": "Major", "coinsuranceINPct": 93, "coinsuranceOONPct": 77, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}], "insureds": [{"isFullTimeStudent": false, "relationshipToPrimaryInsuredCd": "Dependent<PERSON><PERSON><PERSON>", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "isMain": true, "_type": "CapDentalPolicyInfoInsuredDetailsEntity"}], "_modelName": "CapDentalUnverifiedPolicy", "_modelVersion": "1", "_modelType": "UnverifiedPolicy", "_timestamp": "2021-10-27T16:31:32.452+03:00", "_archived": false, "_type": "CapDentalUnverifiedPolicy", "_key": {"rootId": "4c12931a-f502-4bb4-833e-cc5e79d8d4d5", "revisionNo": 1, "parentId": "4c12931a-f502-4bb4-833e-cc5e79d8d4d5", "id": "4c12931a-f502-4bb4-833e-cc5e79d8d4d5"}}