<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-repository</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>versioning-api</artifactId>
        </dependency>

        <!-- CAP Common Versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-versioning-repository</artifactId>
        </dependency>

        <!--- Testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.utils</groupId>
            <artifactId>testing-utils</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>