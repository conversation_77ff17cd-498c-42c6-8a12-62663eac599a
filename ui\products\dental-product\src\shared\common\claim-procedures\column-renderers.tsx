/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'

import {CapDentalLoss} from '@eisgroup/dental-models'
import {Field, LookupValue} from '@eisgroup/form'
import {LocalizationUtils, t} from '@eisgroup/i18n'

import {getDateFormattingByLocale} from '../../../utils/common/utils'
import {moneyByLocale} from '../../../utils/helpers'
import {ProcedureCodeSearchWithFormField} from '../../components/procedure-code-search/ProcedureCodeSearchWithFormField'
import {CLAIM_PROCEDURE_FORM_ITEM_ROW, DIAGNOSIS_TABLE_ITEM_ROW} from './classnames'
import {createValidators} from './validators'

import CapDentalProcedureEntity = CapDentalLoss.CapDentalProcedureEntity
import CapDentalDiagnosisCodeEntity = CapDentalLoss.CapDentalDiagnosisCodeEntity
import dateValue = LocalizationUtils.dateValue

const {Datepicker, Input, InputNumber, MoneyInput, LookupSelect} = Field

export const renderDateOfService = (
    entity: CapDentalProcedureEntity,
    isEditable: boolean,
    prefixName: string,
    dateOfServiceRequired: boolean,
    receiveDate?: Date
) => {
    if (!isEditable) {
        return (
            <Field.Text
                name={`${prefixName}.dateOfService`}
                format={value => (value ? dateValue(value).toString() : '')}
            />
        )
    }

    return (
        <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
            <Datepicker
                {...getDateFormattingByLocale()}
                name={`${prefixName}.dateOfService`}
                valueType='DATE'
                required={dateOfServiceRequired}
                validate={createValidators.dateOfService(receiveDate)}
            />
        </div>
    )
}

export const renderProcedureCode = (
    entity: CapDentalProcedureEntity,
    isEditable: boolean,
    prefixName: string,
    procedureCodeList: LookupValue[]
) => {
    if (!isEditable) {
        return procedureCodeList.filter(v => v.code === entity.procedureCode)[0]?.displayValue
    }

    return (
        <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
            <ProcedureCodeSearchWithFormField
                procedureCodeList={procedureCodeList}
                name={`${prefixName}.procedureCode`}
                requried
                validate={createValidators.procedureCode()}
            />
        </div>
    )
}

export const renderQuantity = (entity: CapDentalProcedureEntity, isEditable: boolean, prefixName: string) => {
    if (!isEditable) {
        return entity.quantity
    }

    return (
        <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
            <InputNumber
                name={`${prefixName}.quantity`}
                required
                validate={createValidators.quantity()}
                allowDecimal={false}
            />
        </div>
    )
}

export const renderSubmittedFee = (entity: CapDentalProcedureEntity, isEditable: boolean, prefixName: string) => {
    if (!isEditable) {
        const submittedFeeStr = entity.submittedFee
            ? moneyByLocale(entity.submittedFee?.amount)
            : t('dental-product:empty')
        return submittedFeeStr
    }

    return (
        <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
            <MoneyInput name={`${prefixName}.submittedFee`} required validate={createValidators.submittedFee()} />
        </div>
    )
}

export const renderDiagnosisQualifier = (
    entity: CapDentalDiagnosisCodeEntity,
    isEditable: boolean,
    prefixName: string,
    qualifierOptions: LookupValue[]
) => {
    if (!isEditable) {
        return qualifierOptions.find(v => v.code === entity.qualifier)?.displayValue
    }

    return (
        <div className={DIAGNOSIS_TABLE_ITEM_ROW}>
            <LookupSelect
                name={`${prefixName}.qualifier`}
                lookupName='CapDNDiagnosisCodesListQualifier'
                required
                validate={createValidators.diagnosisQualifier()}
                optionRender={option => <>{`${option.value?.code} - ${option.value?.displayValue}`}</>}
            />
        </div>
    )
}

export const renderDiagnosisCode = (entity: CapDentalDiagnosisCodeEntity, isEditable: boolean, prefixName: string) => {
    if (!isEditable) {
        return entity.code
    }

    return (
        <div className={DIAGNOSIS_TABLE_ITEM_ROW}>
            <Input name={`${prefixName}.code`} required validate={createValidators.diagnosisCode()} />
        </div>
    )
}
