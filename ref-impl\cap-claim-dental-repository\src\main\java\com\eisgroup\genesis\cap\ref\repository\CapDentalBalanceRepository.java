/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;

/**
 * Repository to store and retrieve dental balance.
 *
 * <AUTHOR>
 * @since 22.13
 */
public interface CapDentalBalanceRepository {

    /**
     * Saves dental balance to storage.
     *
     * @param dentalBalance dental balance to be saved
     * @return saved dental balance
     *
     * <AUTHOR>
     * @since 22.13
     */
    Lazy<CapDentalBalanceEntity> save(CapDentalBalanceEntity dentalBalance);

    /**
     * Retrieves dental balance record from storage.
     *
     * @param key identifier key of dental balance, which should be retrieved
     * @param modelName used to resolve correct storage
     * @return loaded dental balance or error if such entity was not found
     *
     * <AUTHOR>
     * @since 22.13
     */
    Lazy<CapDentalBalanceEntity> load(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> key, String modelName);
}
