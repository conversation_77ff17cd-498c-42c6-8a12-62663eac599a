/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.ReadStatement;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.columnstore.statement.WriteStatement;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.json.IdentifiableEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import java.util.NoSuchElementException;

/**
 * Default implementation of {@link CapDentalPatientHistoryRepository}.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalPatientHistoryRepositoryImpl implements CapDentalPatientHistoryRepository {

    private static final ModelRepository<DomainModel> domainModelRepository = ModelRepositoryFactory.getRepositoryFor(DomainModel.class);

    private final ColumnStore columnStore;

    private final StatementBuilderFactory statementFactory;

    public CapDentalPatientHistoryRepositoryImpl(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        this.columnStore = columnStore;
        this.statementFactory = statementFactory;
    }

    @Override
    public Lazy<CapDentalPatientHistoryEntity> save(CapDentalPatientHistoryEntity patientHistory) {
        DomainModel model = resolveModel(patientHistory.getModelName());
        KeyTraversalUtil.traverseRoot(patientHistory.toJson(), model);
        Class<CapDentalPatientHistoryEntity> modelType = resolveType(model, model.getRoot().getType());

        WriteStatement<CapDentalPatientHistoryEntity> ws = statementFactory.write(modelType).entity(patientHistory).includeChildren().build();
        return columnStore.execute(resolveSchemaName(model), ws)
                .map(rs -> patientHistory);
    }

    @Override
    public Lazy<CapDentalPatientHistoryEntity> load(RootEntityKey key, String modelName) {
        DomainModel model = resolveModel(modelName);
        Class<CapDentalPatientHistoryEntity> modelType = resolveType(model, model.getRoot().getType());
        ReadStatement<CapDentalPatientHistoryEntity> rs = statementFactory.read(modelType).where(key).build();
        return columnStore.execute(resolveSchemaName(model), rs)
                .findFirst()
                .or(() -> Lazy.error(NoSuchElementException::new));
    }

    protected DomainModel resolveModel(String modelName) {
        return domainModelRepository.getActiveModel(modelName);
    }

    protected <T extends IdentifiableEntity> Class<T> resolveType(DomainModel model, String typeName) {
        return (Class<T>) ModelInstanceFactory.typeFor(model.getName(), typeName);
    }

    protected String resolveSchemaName(DomainModel model) {
        return ModeledEntitySchemaResolver.getSchemaNameUsing(model, null);
    }

}
