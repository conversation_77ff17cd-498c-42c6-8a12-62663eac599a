import {Privileges} from '../../../../../../utils'
import {DentalClaimState} from '../../../../../../utils/types/enums'

export enum ClaimActions {
    ADJUST_CLAIM = 'ADJUST_CLAIM',
    CHANGE_CLAIM_SUBSTATUS = 'CHANGE_CLAIM_SUBSTATUS',
    SUSPEND_CLAIM = 'SUSPEND_CLAIM',
    RECOVERY_CLAIM = 'RECOVERY_CLAIM'
}

export const stateAvailableActions: {
    [key: string]: {action: ClaimActions; privileges: string[]}[]
} = {
    [DentalClaimState.Open]: [
        {
            action: ClaimActions.ADJUST_CLAIM,
            privileges: [Privileges.UPDATE_LOSS]
        },
        {
            action: ClaimActions.SUSPEND_CLAIM,
            privileges: [Privileges.SUSPEND_LOSS]
        }
    ],
    [DentalClaimState.Pending]: [
        {
            action: ClaimActions.CHANGE_CLAIM_SUBSTATUS,
            privileges: [Privileges.SET_LOSS_SUBSTATUS]
        },
        {
            action: ClaimActions.ADJUST_CLAIM,
            privileges: [Privileges.UPDATE_LOSS]
        }
    ],
    [DentalClaimState.Incomplete]: [
        {
            action: ClaimActions.CHANGE_CLAIM_SUBSTATUS,
            privileges: [Privileges.SET_LOSS_SUBSTATUS]
        }
    ],
    [DentalClaimState.Suspended]: [
        {
            action: ClaimActions.RECOVERY_CLAIM,
            privileges: [Privileges.RECOVERY_LOSS]
        }
    ]
}
