/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.repository.CapDentalProviderResolver;
import com.eisgroup.genesis.http.factory.HttpClientFactory;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalResolverConfigTest {

    @InjectMocks
    private CapDentalResolverConfig config;

    @Mock
    private HttpClientFactory httpClientFactory;
    @Mock
    private ServiceAccessRunner serviceAccessRunner;

    @Test
    public void shouldCapDentalProviderResolver() {
        CapDentalProviderResolver result = config.capDentalProviderResolver(httpClientFactory, null, serviceAccessRunner);
        assertThat(result, notNullValue());
    }
}