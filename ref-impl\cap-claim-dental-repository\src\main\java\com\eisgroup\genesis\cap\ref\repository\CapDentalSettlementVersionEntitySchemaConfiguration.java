/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.entity.metadata.config.EntitySchemaConfiguration;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.model.Variation;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.versioning.Version;

import java.util.Arrays;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * Entity schema configuration for  settlement versioning
 * <AUTHOR>
 * @since  22.6
 */
public class CapDentalSettlementVersionEntitySchemaConfiguration implements EntitySchemaConfiguration {

    private static final String DENTAL_SETTLEMENT = "CapDentalSettlement";

    private static final ModelRepository<DomainModel> modelRepository = ModelRepositoryFactory
            .getRepositoryFor(DomainModel.class);

    @Override
    public Collection<String> getAppliesTo() {
        return modelRepository.getAllModels().stream().map(modelRepository::getActiveModel)
                .filter(model -> DENTAL_SETTLEMENT.equals(model.getName()))
                .map(model -> ModeledEntitySchemaResolver.getSchemaNameUsing(model, Variation.INVARIANT))
                .collect(Collectors.toList());
    }
    @Override
    public Collection<Class<?>> getScanClassesNames() {
        return Arrays.asList(Version.class);
    }
}
