/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.model.capdentalsettlementindex.CapDentalSettlementIdx;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.parameter.TransformationInput;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import com.google.gson.JsonObject;

/**
 * {@link CapDentalSettlementIdx} entities resolver.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalSettlementIndexResolver {

    private static final String LOAD_SETTLEMENT_INDEX_TRANSFORMATION = "CapLoadDentalSettlementIndex";

    private final ModeledTransformationService modeledTransformationService;
    private final ModelRepository<TransformationModel> transformationRepository;

    public CapDentalSettlementIndexResolver(ModeledTransformationService modeledTransformationService,
                                            ModelRepository<TransformationModel> transformationRepository) {
        this.modeledTransformationService = modeledTransformationService;
        this.transformationRepository = transformationRepository;
    }

    public Streamable<CapDentalSettlementIdx> resolveSettlementIndex(EntityLink<RootEntity> originSource) {
        TransformationModel transformationModel = transformationRepository.getActiveModel(LOAD_SETTLEMENT_INDEX_TRANSFORMATION);
        JsonObject originSourceKeyFilter = new JsonObject();
        originSourceKeyFilter.addProperty(CapDentalSettlementIdx.LOSS_ID_ATTR, originSource.getURIString());
        return modeledTransformationService.transformAll(transformationModel,
                        TransformationInput.builder(transformationModel)
                                .generic(originSourceKeyFilter, "originSourceKeyFilter").build())
                .flatMap(transformationOutput -> Streamable.from(transformationOutput.asCollection())
                        .cast(CapDentalSettlementIdx.class));
    }
}

