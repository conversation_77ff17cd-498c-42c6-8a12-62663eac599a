/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepositoryImpl;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import org.springframework.context.annotation.Bean;

/**
 * Contains beans related to dental patient history repository.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalPatientHistoryRepositoryConfig {

    @Bean
    public CapDentalPatientHistoryRepository capDentalPatientHistoryRepository(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        return new CapDentalPatientHistoryRepositoryImpl(columnStore, statementFactory);
    }
}
