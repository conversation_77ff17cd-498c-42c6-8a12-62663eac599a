package com.eisgroup.genesis.facade;

import com.eisgroup.genesis.facade.endpoint.load.LoadEntityRestEndpoint;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalBalanceChangeLogFacadeTest {

    private static final String MODEL_NAME = "CapDentalBalanceChangeLog";

    private static final String MODEL_TYPE = "CapBalanceChangeLog";

    private CapDentalBalanceChangeLogFacade facade;

    @Before
    public void setup() {
        facade = new CapDentalBalanceChangeLogFacade();
    }

    @Test
    public void shouldReturnCorrectEndpoints() {
        // when
        var result = facade.getEndpoints();
        // then
        assertThat(result, notNullValue());
        assertThat(result.stream().anyMatch(endoint -> LoadEntityRestEndpoint.class.isAssignableFrom(endoint.getClass())), equalTo(true));
    }

    @Test
    public void shouldReturnModelName() {
        assertThat(facade.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(facade.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnVersion() {
        assertThat(facade.getFacadeVersion(), equalTo(1));
    }
}
