/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import static org.springframework.util.CollectionUtils.isEmpty;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.versioning.CapChangeHistoryEntity;
import com.eisgroup.genesis.cap.common.versioning.CapChangeHistoryKey;
import com.eisgroup.genesis.cap.common.versioning.repository.CapChangeHistoryRepository;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.ReadStatement;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.columnstore.statement.WriteStatement;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Dental specific change history repository to expand search functionality with additional filter keys
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalChangeHistoryRepository implements CapChangeHistoryRepository {

    private static final Logger logger = LoggerFactory.getLogger(CapDentalChangeHistoryRepository.class);

    private final ColumnStore columnStore;

    private final StatementBuilderFactory statementFactory;

    private String schemaName;

    public CapDentalChangeHistoryRepository(ColumnStore columnStore, StatementBuilderFactory statementFactory, String schemaName) {
        this.columnStore = columnStore;
        this.statementFactory = statementFactory;
        this.schemaName = schemaName;
    }

    @Override
    public Lazy<CapChangeHistoryEntity> save(CapChangeHistoryEntity entity) {
        WriteStatement<CapChangeHistoryEntity> historyWs = statementFactory.write(CapChangeHistoryEntity.class)
                .entity(entity).build();

        return Lazy
                .defer(() -> columnStore.executeBatch(schemaName, new WriteStatement[]{historyWs}).map(rs -> {
                    logger.debug("Result of change history by key [{}] persistence is {}", entity.getKey(), rs);
                    return entity;
                }));
    }


    @Override
    public Streamable<CapChangeHistoryEntity> findChangeHistoryRecords(String URI, Map<String, String> filter) {
        ReadStatement<CapChangeHistoryEntity> rs = statementFactory.read(CapChangeHistoryEntity.class)
            .where(matcher -> matcher.matches(CapChangeHistoryKey.ATTRIBUTE_URI, URI).build()).build();
        Streamable<CapChangeHistoryEntity> records = columnStore.execute(schemaName, rs);

        if (isEmpty(filter)) {
            return records;
        }

        return Streamable.defer(() -> records.filter(record -> isApplicable(record, filter)));
    }

    private boolean isApplicable(CapChangeHistoryEntity entity, Map<String, String> filter) {
        if (filter == null || filter.isEmpty()) {
            return true;
        }

        return filter.entrySet()
                .stream()
                .allMatch(entry -> StringUtils.equals(entry.getValue(), entity.getFilter().getValues().get(entry.getKey())));

    }

}
