<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean primary="true" id="capDentalChangeHistoryRepository"
          class="com.eisgroup.genesis.cap.ref.repository.CapDentalChangeHistoryRepository">
        <constructor-arg name="columnStore" ref="columnStore"/>
        <constructor-arg name="statementFactory" ref="statementBuilderFactory"/>
        <constructor-arg name="schemaName" value="${cap.versioning.schema.name}"/>
    </bean>

    <bean class="com.eisgroup.genesis.cap.ref.repository.config.CapDentalSettlementRepositoryConfig"/>

    <bean class="com.eisgroup.genesis.cap.ref.repository.config.CapDentalResolverConfig"/>

    <bean class="com.eisgroup.genesis.cap.ref.repository.config.CapDentalPaymentScheduleRepositoryConfig"/>

</beans>