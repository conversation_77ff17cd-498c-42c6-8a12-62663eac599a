/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {t} from '@eisgroup/i18n'

import {
    composeValidators,
    isDateAfterCompareDate,
    isDateFuture,
    isValuePositiveForFieldName,
    required,
    validateMinMaxValue
} from '../../../utils/common/ValidationUtils'

export const createValidators = {
    dateOfService: (receiveDate?: Date) =>
        composeValidators(
            required(
                'dental-product:field_mandatory_message',
                t('dental-product:dental-claim-claim-details-services-date-of-service')
            ),
            isDateFuture('dental-product:date_of_service_cannot_be_future_date'),
            isDateAfterCompareDate('dental-product:date_of_service_cannot_be_after_received_date', receiveDate)
        ),

    procedureCode: () =>
        required(
            'dental-product:field_mandatory_message',
            t('dental-product:dental-claim-claim-details-services-procedure-code')
        ),

    quantity: () =>
        composeValidators(
            required(
                'dental-product:field_mandatory_message',
                t('dental-product:dental-claim-claim-details-services-quantity')
            ),
            validateMinMaxValue(1, 99, t('dental-product:dental-claim-claim-details-services-quantity'))
        ),

    submittedFee: () =>
        composeValidators(
            required(
                'dental-product:field_mandatory_message',
                t('dental-product:dental-claim-claim-details-services-charges')
            ),
            v => isValuePositiveForFieldName(v, t('dental-product:dental-claim-claim-details-services-charges'))
        ),

    paymentFrequency: () =>
        required(
            'dental-product:field_mandatory_message',
            t('dental-product:dental-claim-claim-details-services-payment-frequency')
        ),

    monthsOfTreatment: () =>
        composeValidators(
            required(
                'dental-product:field_mandatory_message',
                t('dental-product:dental-claim-claim-details-services-months-of-treatment')
            ),
            validateMinMaxValue(1, 36, t('dental-product:dental-claim-claim-details-services-months-of-treatment'))
        ),

    diagnosisQualifier: () =>
        required(
            'dental-product:field_mandatory_message',
            t('dental-product:dental-claim-edit-procedure-details-diagnosis-qualifier')
        ),

    diagnosisCode: () =>
        required(
            'dental-product:field_mandatory_message',
            t('dental-product:dental-claim-edit-procedure-details-diagnosis-code')
        )
}
