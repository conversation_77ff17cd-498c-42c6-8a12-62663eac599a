package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.common.versioning.CapChangeHistoryEntity;
import com.eisgroup.genesis.cap.ref.repository.CapDentalChangeHistoryRepository;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.columnstore.statement.WriteStatement;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class CapDentalChangeHistoryRepositoryTest {

    @Mock
    private StatementBuilderFactory statementFactory;
    @Mock
    private WriteStatement.Builder writeBuilder;
    @Mock
    private WriteStatement writeStatement;
    @Mock
    private ColumnStore columnStore;
    @InjectMocks
    private CapDentalChangeHistoryRepository repository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void shouldExecuteSaveSuccessfully() {
        //given
        CapChangeHistoryEntity entity = new CapChangeHistoryEntity();
        //when
        when(statementFactory.write(any())).thenReturn(writeBuilder);
        when(writeBuilder.includeChildren()).thenReturn(writeBuilder);
        when(writeBuilder.entity(entity)).thenReturn(writeBuilder);
        when(writeBuilder.build()).thenReturn(writeStatement);
        when(columnStore.executeBatch(null, new WriteStatement[]{writeStatement})).thenReturn(Lazy.of(1));
        //then
        TestStreamable.create(repository.save(entity))
                .assertNoErrors()
                .assertValueCount(1);

    }
}
