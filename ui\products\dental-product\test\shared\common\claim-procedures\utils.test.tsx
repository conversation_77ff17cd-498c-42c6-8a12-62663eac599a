/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {render} from '@testing-library/react'
import {describe, expect, it, vi} from 'vitest'

import {CLAIM_MODE} from '../../../../src/pages/dental-edit-page/constants'
import {
    getColumns,
    getDefaultColumns,
    getDiagnosisColumns,
    getDiagnosisViewColumns,
    renderContent
} from '../../../../src/shared/common/claim-procedures/utils'
import {TransactionType} from '../../../../src/utils/common/constants'
import {wrapInForm} from '../../../support/TestUtils'

// Mock the i18n module
vi.mock('@eisgroup/i18n', async importOriginal => {
    const actual = (await importOriginal()) as any
    return {
        ...actual,
        t: (key: string) => {
            const translations = {
                'dental-product:dental-claim-claim-details-services-line': 'Line',
                'dental-product:dental-claim-claim-details-services-date-of-service': 'Date of Service',
                'dental-product:dental-claim-claim-details-services-procedure-code': 'Procedure Code',
                'dental-product:dental-claim-claim-details-services-quantity': 'Quantity',
                'dental-product:dental-claim-claim-details-services-charges': 'Charges',
                'dental-product:dental-claim-claim-details-services-payment-frequency': 'Payment Frequency',
                'dental-product:dental-claim-claim-details-services-months-of-treatment': 'Months of Treatment',
                'dental-product:dental-claim-edit-procedure-details-diagnosis-qualifier': 'Diagnosis List Qualifier',
                'dental-product:dental-claim-edit-procedure-details-diagnosis-code': 'Diagnosis Code',
                'dental-product:empty': 'Empty'
            }
            return translations[key] || key
        }
    }
})

// Mock the column renderers
vi.mock('../../../../src/shared/common/claim-procedures/column-renderers', () => ({
    renderDateOfService: vi.fn(() => <div>Date of Service Renderer</div>),
    renderProcedureCode: vi.fn(() => <div>Procedure Code Renderer</div>),
    renderQuantity: vi.fn(() => <div>Quantity Renderer</div>),
    renderSubmittedFee: vi.fn(() => <div>Submitted Fee Renderer</div>),
    renderDiagnosisQualifier: vi.fn(() => <div>Diagnosis Qualifier Renderer</div>),
    renderDiagnosisCode: vi.fn(() => <div>Diagnosis Code Renderer</div>)
}))

describe('utils', () => {
    describe('getDefaultColumns', () => {
        const mockProcedureCodeList = [
            {code: 'D0120', displayValue: 'Periodic oral evaluation'},
            {code: 'D1110', displayValue: 'Adult prophylaxis'}
        ]

        const mockEntityInfo = {
            prefixName: 'test.prefix'
        }

        const mockReceiveDate = new Date('2023-01-01')

        it('should return correct columns structure', () => {
            const isEditable = vi.fn(() => true)
            const getEntityInfo = vi.fn(() => mockEntityInfo)

            const columns = getDefaultColumns(
                isEditable,
                getEntityInfo,
                mockProcedureCodeList,
                TransactionType.ACTUAL_SERVICES,
                mockReceiveDate
            )

            expect(columns).toHaveLength(5)
            expect(columns[0].title).toBe('Line')
            expect(columns[1].title).toBe('Date of Service')
            expect(columns[2].title).toBe('Procedure Code')
            expect(columns[3].title).toBe('Quantity')
            expect(columns[4].title).toBe('Charges')
        })

        it('should render line number correctly', () => {
            const isEditable = vi.fn(() => true)
            const getEntityInfo = vi.fn(() => mockEntityInfo)

            const columns = getDefaultColumns(
                isEditable,
                getEntityInfo,
                mockProcedureCodeList,
                TransactionType.ACTUAL_SERVICES,
                mockReceiveDate
            )

            const lineColumn = columns[0]
            expect(lineColumn.render).toBeDefined()
            const mockProcedureEntity = {} as any
            expect(lineColumn.render!('', mockProcedureEntity, 0)).toBe('01')
            expect(lineColumn.render!('', mockProcedureEntity, 9)).toBe('10')
        })
    })

    describe('getColumns', () => {
        const mockProcedureCodeList = [
            {code: 'D0120', displayValue: 'Periodic oral evaluation'},
            {code: 'D1110', displayValue: 'Adult prophylaxis'}
        ]

        const mockEntityInfo = {
            prefixName: 'test.prefix'
        }

        const mockReceiveDate = new Date('2023-01-01')

        it('should return correct columns structure with extended fields', () => {
            const isEditable = vi.fn(() => true)
            const getEntityInfo = vi.fn(() => mockEntityInfo)

            const columns = getColumns(
                isEditable,
                getEntityInfo,
                mockProcedureCodeList,
                CLAIM_MODE.INTAKE,
                TransactionType.ORTHODONTIC_SERVICES,
                mockReceiveDate
            )

            expect(columns).toHaveLength(7)
            expect(columns[0].title).toBe('Line')
            expect(columns[1].title).toBe('Date of Service')
            expect(columns[2].title).toBe('Procedure Code')
            expect(columns[3].title).toBe('Quantity')
            expect(columns[4].title).toBe('Charges')
            expect(columns[5].title).toBe('Payment Frequency')
            expect(columns[6].title).toBe('Months of Treatment')
        })
    })

    describe('getDiagnosisColumns', () => {
        const qualifierOptions = [{code: 'D0120', displayValue: 'Periodic oral evaluation'}]

        const mockEntityInfo = {
            prefixName: 'test.prefix'
        }

        it('should return correct diagnosis columns structure', () => {
            const isEditable = vi.fn(() => true)
            const getEntityInfo = vi.fn(() => mockEntityInfo)

            const columns = getDiagnosisColumns(isEditable, getEntityInfo, qualifierOptions)

            expect(columns).toHaveLength(2)
            expect(columns[0].title).toBe('Diagnosis List Qualifier')
            expect(columns[1].title).toBe('Diagnosis Code')
        })
    })

    describe('getDiagnosisViewColumns', () => {
        const qualifierOptions = [{code: 'D0120', displayValue: 'Periodic oral evaluation'}]

        it('should return view-only diagnosis columns', () => {
            const columns = getDiagnosisViewColumns(qualifierOptions)

            expect(columns).toHaveLength(2)
            expect(columns[0].title).toBe('Diagnosis List Qualifier')
            expect(columns[1].title).toBe('Diagnosis Code')
        })

        it('should render diagnosis qualifier correctly in view mode', () => {
            const columns = getDiagnosisViewColumns(qualifierOptions)
            const qualifierColumn = columns[0]

            const entity = {qualifier: 'D0120', _type: 'CapDentalDiagnosisCodeEntity', _key: {}} as any
            const result = qualifierColumn.render(entity)

            expect(result).toBe('Periodic oral evaluation')
        })

        it('should return Empty for missing qualifier in view mode', () => {
            const columns = getDiagnosisViewColumns(qualifierOptions)
            const qualifierColumn = columns[0]

            const entity = {qualifier: 'UNKNOWN', _type: 'CapDentalDiagnosisCodeEntity', _key: {}} as any
            const result = qualifierColumn.render(entity)

            expect(result).toBe('Empty')
        })
    })

    describe('renderContent', () => {
        const mockControls = {
            preauthorizationNumber: <div>Preauth</div>,
            dateOfService: <div>Date</div>,
            procedureCode: <div>Procedure</div>,
            quantity: <div>Qty</div>,
            submittedFee: <div>Fee</div>,
            toothArea: <div>Tooth Area</div>,
            surfaces: <div>Surfaces</div>,
            toothSystem: <div>Tooth System</div>,
            toothCodes: <div>Tooth Codes</div>,
            diagnosis: <div>Diagnosis</div>,
            dividerOrthDetails: <div>Ortho Divider</div>,
            orthoMonthQuantity: <div>Ortho Months</div>,
            appliancePlacedDate: <div>Appliance Date</div>,
            orthoFrequencyCd: <div>Frequency</div>,
            downPayment: <div>Down Payment</div>,
            dividerProsthesisDetails: <div>Prosthesis Divider</div>,
            priorProsthesisPlacementDate: <div>Prior Date</div>
        }

        it('should render basic procedure fields', () => {
            const {container} = render(
                wrapInForm(renderContent(mockControls, 0, TransactionType.ACTUAL_SERVICES) as React.ReactElement)
            )

            expect(container.textContent).toContain('Preauth')
            expect(container.textContent).toContain('Date')
            expect(container.textContent).toContain('Procedure')
            expect(container.textContent).toContain('Qty')
            expect(container.textContent).toContain('Fee')
            expect(container.textContent).toContain('Tooth Area')
            expect(container.textContent).toContain('Surfaces')
            expect(container.textContent).toContain('Tooth System')
            expect(container.textContent).toContain('Tooth Codes')
            expect(container.textContent).toContain('Diagnosis')
        })

        it('should render orthodontic fields when procedure type is orthodontic', () => {
            const {container} = render(
                wrapInForm(renderContent(mockControls, 0, TransactionType.ORTHODONTIC_SERVICES) as React.ReactElement)
            )

            expect(container.textContent).toContain('Ortho Divider')
            expect(container.textContent).toContain('Ortho Months')
            expect(container.textContent).toContain('Appliance Date')
            expect(container.textContent).toContain('Frequency')
            expect(container.textContent).toContain('Down Payment')
            expect(container.textContent).toContain('Prosthesis Divider')
            expect(container.textContent).toContain('Prior Date')
        })

        it('should not render orthodontic fields for non-orthodontic procedures', () => {
            const {container} = render(
                wrapInForm(renderContent(mockControls, 0, TransactionType.ACTUAL_SERVICES) as React.ReactElement)
            )

            expect(container.textContent).not.toContain('Ortho Divider')
            expect(container.textContent).not.toContain('Ortho Months')
            expect(container.textContent).not.toContain('Appliance Date')
        })
    })
})
