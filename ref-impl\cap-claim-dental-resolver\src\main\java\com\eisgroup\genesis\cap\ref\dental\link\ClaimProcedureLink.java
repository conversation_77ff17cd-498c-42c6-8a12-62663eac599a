/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link;

import java.util.UUID;

import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Dental claim procedure link used to parse {@link EntityLink} and construct entity URI string.
 *
 * <AUTHOR>
 * @since 22.8
 */
public class ClaimProcedureLink {

    public static final String ENTITY_URI_FORMAT = "capPatient://claimHistory/%s/%s/%s";

    private final String type;
    private final String modelName;
    private final UUID settlementId;
    private final UUID procedureId;

    public ClaimProcedureLink(EntityLink<?> link) {
        this.type = link.getURI().getHost();
        String path = link.getURI().getPath();
        String[] pathParams = path.split(EntityLink.PATH_SEPARATOR);
        this.modelName = pathParams[1];
        this.settlementId = UUID.fromString(pathParams[2]);
        this.procedureId = UUID.fromString(pathParams[3]);
    }

    public ClaimProcedureLink(String modelName, UUID settlementId, UUID procedureId) {
        this.type = "claimHistory";
        this.modelName = modelName;
        this.settlementId = settlementId;
        this.procedureId = procedureId;
    }

    public String toURI() {
        return String.format(ENTITY_URI_FORMAT, getModelName(), getSettlementId(), getProcedureId());
    }

    public String getType() {
        return type;
    }

    public String getModelName() {
        return modelName;
    }

    public UUID getSettlementId() {
        return settlementId;
    }

    public UUID getProcedureId() {
        return procedureId;
    }
}
