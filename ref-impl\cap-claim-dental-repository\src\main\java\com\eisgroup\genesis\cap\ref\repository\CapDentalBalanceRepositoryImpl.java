/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.ReadStatement;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.columnstore.statement.WriteStatement;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.json.IdentifiableEntity;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import java.util.NoSuchElementException;

/**
 * Default implementation of {@link CapDentalBalanceRepository}.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalBalanceRepositoryImpl implements CapDentalBalanceRepository {

    private static final ModelRepository<DomainModel> domainModelRepository = ModelRepositoryFactory.getRepositoryFor(DomainModel.class);

    private final ColumnStore columnStore;

    private final StatementBuilderFactory statementFactory;

    public CapDentalBalanceRepositoryImpl(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        this.columnStore = columnStore;
        this.statementFactory = statementFactory;
    }

    @Override
    public Lazy<CapDentalBalanceEntity> save(CapDentalBalanceEntity dentalBalance) {
        DomainModel model = resolveModel(dentalBalance.getModelName());
        KeyTraversalUtil.traverseRoot(dentalBalance.toJson(), model);
        Class<CapDentalBalanceEntity> modelType = resolveType(model, model.getRoot().getType());

        WriteStatement<CapDentalBalanceEntity> ws = statementFactory.write(modelType).entity(dentalBalance).includeChildren().build();
        return columnStore.execute(resolveSchemaName(model), ws)
                .map(rs -> dentalBalance);
    }

    @Override
    public Lazy<CapDentalBalanceEntity> load(RootEntityKey key, String modelName) {
        DomainModel model = resolveModel(modelName);
        Class<CapDentalBalanceEntity> modelType = resolveType(model, model.getRoot().getType());
        ReadStatement<CapDentalBalanceEntity> rs = statementFactory.read(modelType).where(key).build();
        return columnStore.execute(resolveSchemaName(model), rs)
            .findFirst()
            .or(() -> Lazy.error(NoSuchElementException::new));
    }

    protected DomainModel resolveModel(String modelName) {
        return domainModelRepository.getActiveModel(modelName);
    }

    protected <T extends IdentifiableEntity> Class<T> resolveType(DomainModel model, String typeName) {
        return (Class<T>) ModelInstanceFactory.typeFor(model.getName(), typeName);
    }

    protected String resolveSchemaName(DomainModel model) {
        return ModeledEntitySchemaResolver.getSchemaNameUsing(model, null);
    }
}
