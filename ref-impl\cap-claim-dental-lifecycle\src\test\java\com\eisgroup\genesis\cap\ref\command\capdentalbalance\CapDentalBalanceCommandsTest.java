package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class CapDentalBalanceCommandsTest {

    @Test
    public void testConstantsHaveCorrectValues() {
        assertEquals("initBalance", CapDentalBalanceCommands.INIT_DENTAL_BALANCE);
        assertEquals("updateBalance", CapDentalBalanceCommands.UPDATE_DENTAL_BALANCE);
        assertEquals("calculateLossBalance", CapDentalBalanceCommands.CALCULATE_DENTAL_BALANCE);
    }
}
