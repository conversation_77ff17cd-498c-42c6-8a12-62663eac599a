@EventListener("write")
@TargetEntityCommand("writeTransaction")
Transformation UPIndToAccumulatorTx {
    Input {
        Ext CapDentalUnverifiedPolicy.CapDentalUnverifiedPolicy as policy
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity
    }

     Attr transactionTimestamp is Now()
     Attr policyURI is Add("capPolicy://CapUP/", ToExtLink(policy, "geroot")._uri)
     Attr customerURI is First(policy.insureds[filterMain].registryTypeId)
     Attr sourceURI is ToExtLink(policy)._uri

     Var individualDeductibleTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualDeductible_Annual_Dental", First(policy.deductibleDetails).individualBasicINNAnnualDeductible.amount)
     Var familyDeductibleTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_FamilyDeductible_Annual_Dental", First(policy.deductibleDetails).individualMajorINNAnnualDeductible.amount)

     Var individualMaximumTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualMaximum_Annual_Dental", First(policy.dentalMaximums).individualBasicINNAnnualMaximum.amount)

     Var cosmeticMaximumTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_CosmeticMaximum_Annual_CosmeticServices", First(policy.dentalMaximums).individualMajorINNAnnualMaximum.amount)

     Var tmjMaximumTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_TMJMaximum_Lifetime_TMJ", First(policy.dentalMaximums).individualPreventiveINNAnnualMaximum.amount)

     Var implantsTx is  mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_ImplantsMaximum_Annual_Implants", First(policy.dentalMaximums).implantsINNAnnualMaximum.amount)
     Var orthoTx is  mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_OrthoMaximum_Annual_Orthodontics", First(policy.dentalMaximums).orthoINNAnnualMaximum.amount)

     Var mappedData is FlatMap(individualDeductibleTx, familyDeductibleTx, individualMaximumTx, cosmeticMaximumTx, tmjMaximumTx, implantsTx, orthoTx)
     Attr data is mappedData[filterNonNullAmount]

     Producer mapParticipantsToTx(party, policy, txType, txAmount) {
         Attr type is txType
         Attr amount is txAmount
         Attr extension is New() {
            Attr term is Super().policy.term
            Attr networkType is "INN"
            Attr _type is "JsonType"
         }
         Attr transactionDate is Now()
         Attr policyTermDetails is policy.term

         Attr party is createExtLink(party.registryTypeId)
         // ExtLink resource : RootEntity
     }

    Filter filterMain {
        isMain == TRUE
    }

    Filter filterNonNullAmount {
        !Equals(amount, Null())
    }

    Producer createExtLink(uri) {
        Attr _uri is uri
    }
}
