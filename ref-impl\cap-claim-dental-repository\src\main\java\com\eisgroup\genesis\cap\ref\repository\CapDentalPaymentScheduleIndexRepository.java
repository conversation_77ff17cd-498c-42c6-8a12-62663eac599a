/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;

import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

import java.util.List;

/**
 * {@link CapDentalPaymentScheduleIdxEntity} entities resolver.
 *
 * <AUTHOR>
 * @since 22.9
 */
public interface CapDentalPaymentScheduleIndexRepository {

    Streamable<List<CapDentalPaymentScheduleIdxEntity>> load(String applicableState);

    /**
     * Resolves {@link CapDentalPaymentScheduleIdxEntity} entities
     *
     * @param originSource     {@link com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity} link
     * @param applicableStates applicable states for {@link CapDentalPaymentScheduleIdxEntity#getState()}
     * @return resolved {@link CapDentalPaymentScheduleIdxEntity}
     */
    Streamable<CapDentalPaymentScheduleIdxEntity> resolvePaymentScheduleIndex(EntityLink<RootEntity> originSource, List<String> applicableStates);

}
