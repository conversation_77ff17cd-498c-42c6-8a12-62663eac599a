/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {FC, useState} from 'react'

import {UIEngine} from '@eisgroup/builder'
import {ActionMode, EditableTable} from '@eisgroup/dental-core'
import {CapDentalLoss} from '@eisgroup/dental-models'
import {FormApi, LookupValue, PrefixProvider} from '@eisgroup/form'
import {t} from '@eisgroup/i18n'
import {HasLabels} from '@eisgroup/react-components'
import {AntDropdown, AntMenu, AntPopconfirm, notification} from '@eisgroup/ui-kit'
import {SettingEllipsesMedium} from '@eisgroup/ui-kit-icons'

import {DentalFormKrakenServiceDeclaration, FormKrakenInitBlur, RulesEvaluationProps} from '../../../../kraken'
import {useIntakeRootStore, useWithinFormStore} from '../../../../pages/dental-edit-page/store'
import editorConfig from '../../../../ui-builder/editor-config'
import config from '../../../../ui-builder/schemes/IntakePageProcedureForm.builder'
import {ProcedureFormItemsSlot} from '../../../../ui-builder/schemes/slots/ProcedureFormItemsSlot'
import {ProcedureViewItemsSlot} from '../../../../ui-builder/schemes/slots/ProcedureViewItemsSlot'
import {
    CreateOrUpdate,
    DrawerWidth,
    SUBMITTED_PROCEDURES_URI_PATH,
    TransactionType
} from '../../../../utils/common/constants'
import {FormDrawer, FormDrawerActionInfo} from '../../form-drawer'
import {DrawerActions, DrawerFormStateType} from '../../index'
import {PROCEDURE_TABLE_POP_CONFIRM, PROCEDURES_TABLE} from '../classnames'
import {getColumns, getDefaultColumns} from '../utils'

import CapDentalLossEntity = CapDentalLoss.CapDentalLossEntity
import CapDentalProcedureEntity = CapDentalLoss.CapDentalProcedureEntity

export const PROCEDURE_FORM_ID = 'ProcedureFormId'

export type VisibleObject = {
    [key: string]: boolean
}

export interface ProceduresLabels {
    addButtonTitle: string
}

export interface ProceduresTableState {
    actionMenuVisible: VisibleObject
    popConfirmVisible: VisibleObject
    drawerType: CreateOrUpdate
    drawerIndex: string
    isDrawerVisible: boolean
    recordIndex: number
}

export interface ProceduresTableProps extends HasLabels<ProceduresLabels> {
    readonly dentalLoss: CapDentalLossEntity
    /**
     * Array of procedures from state.
     */
    readonly procedures: CapDentalProcedureEntity[]
    readonly updateFormState: (path: string, value: any) => void
    readonly createNewProduceEntity: () => CapDentalProcedureEntity
    readonly listActionMode: ActionMode

    readonly formDrawerProps: FormDrawerActionInfo
    readonly hideButton?: boolean
    readonly isLoading?: boolean
    readonly isMinorTable?: boolean
    readonly customAreaUnderTitle?: React.ReactNode
    readonly isSingleRowPersist?: boolean
    readonly transactionType?: string
    readonly receiveDate?: Date
    readonly procedureCodeList: LookupValue[]
    readonly qualifierOptions: LookupValue[]
}

export const getProceduresItemEntityKey = (entity?: CapDentalProcedureEntity): string => `${entity?._key?.id}`

export const ClaimProceduresTable: FC<ProceduresTableProps> = (props: ProceduresTableProps) => {
    const [state, originSetState] = useState<ProceduresTableState>({
        actionMenuVisible: {},
        popConfirmVisible: {},
        drawerType: CreateOrUpdate.CREATE,
        drawerIndex: '',
        isDrawerVisible: false,
        recordIndex: 0
    })
    const {intakeWizardStore} = useWithinFormStore()
    const {mode} = useIntakeRootStore()

    /**
     * A function to simulate class component setState
     * @param newValue
     */
    const setState = (newValue: Partial<ProceduresTableState>) => {
        originSetState((prev: ProceduresTableState) => {
            return {
                ...prev,
                ...newValue
            }
        })
    }
    const actionMenuItems = (record, id: string, index?: number) => {
        const {popConfirmVisible} = state
        return (
            <AntMenu onClick={({key, domEvent}) => handleMenuClick(key, domEvent, id, record, index)}>
                <AntMenu.Item
                    key='edit'
                    onClick={e => {
                        e.domEvent.stopPropagation()
                    }}
                >
                    <p>{t('dental-product:dental-claim-claim-details-services-edit-service-information')}</p>
                </AntMenu.Item>
                <AntMenu.Item
                    key='view'
                    onClick={e => {
                        e.domEvent.stopPropagation()
                    }}
                >
                    <p>{t('dental-product:dental-claim-claim-details-services-view-service-details')}</p>
                </AntMenu.Item>
                <AntMenu.Item key='delete'>
                    <AntPopconfirm
                        trigger='click'
                        okText={t('dental-product:delete')}
                        cancelText={t('dental-product:cancel')}
                        title={t('dental-product:procedure_remove_pop_message', {code: record.procedureCode})}
                        overlayClassName={PROCEDURE_TABLE_POP_CONFIRM}
                        placement='top'
                        visible={!!popConfirmVisible[id]}
                        onVisibleChange={visible => handlePopConfirmVisibleChange(visible, id)}
                        onCancel={() => handlePopConfirmVisibleChange(false, id)}
                        onConfirm={() => handlePopConfirmDelete(record)}
                    >
                        <p style={{width: '100%'}}>{t('dental-product:delete')}</p>
                    </AntPopconfirm>
                </AntMenu.Item>
            </AntMenu>
        )
    }

    const handlePopConfirmVisibleChange = (visible, id) => {
        const {popConfirmVisible} = state
        popConfirmVisible[id] = visible
        setState({
            popConfirmVisible
        })
    }

    const handlePopConfirmDelete = record => {
        if (!record._key.id) {
            return
        }
        const procedures = props.procedures || []
        const newProcedures = procedures.filter(v => {
            return v._key.id !== record._key.id
        })
        props.updateFormState(SUBMITTED_PROCEDURES_URI_PATH, newProcedures)
    }

    const actionMenu = (record, index?: number) => {
        const id = record?._key?.id
        return (
            <AntDropdown
                key={id}
                trigger={['click']}
                overlay={actionMenuItems(record, id, index)}
                onVisibleChange={visible => handleActionMenuVisibleChange(visible, id)}
                getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
                visible={state.actionMenuVisible[id]}
            >
                <SettingEllipsesMedium />
            </AntDropdown>
        )
    }

    const handleActionMenuVisibleChange = (visible: boolean, id: string) => {
        const {actionMenuVisible} = state
        actionMenuVisible[id] = visible
        setState({
            actionMenuVisible
        })
    }

    const handleMenuClick = (
        key: string,
        domEvent: any,
        id: string,
        record: CapDentalProcedureEntity,
        index?: number
    ) => {
        const {actionMenuVisible} = state
        let serviceIndexStr = ''
        if (index !== undefined) {
            const serviceIndex = (index + 1).toString()
            serviceIndexStr = index + 1 < 10 ? `0${serviceIndex}` : serviceIndex
        }
        if (key === 'edit') {
            props.formDrawerProps.openDrawer(PROCEDURE_FORM_ID)
            setState({
                isDrawerVisible: true,
                drawerType: CreateOrUpdate.UPDATE,
                drawerIndex: serviceIndexStr,
                recordIndex: index || 0
            })
            actionMenuVisible[id] = false
        }
        if (key === 'view') {
            props.formDrawerProps.openDrawer(PROCEDURE_FORM_ID)
            setState({
                isDrawerVisible: true,
                drawerType: CreateOrUpdate.VIEW,
                drawerIndex: serviceIndexStr,
                recordIndex: index || 0
            })
            actionMenuVisible[id] = false
        }
        setState({
            actionMenuVisible
        })
    }

    const renderDrawer = () => {
        const {isDrawerVisible, drawerIndex} = state
        return (
            props.formDrawerProps.openedDrawerKey === PROCEDURE_FORM_ID && (
                <FormDrawer
                    formTitle={t('dental-product:dental-claim-edit-procedure-details-title', {
                        drawerIndex
                    })}
                    visible={isDrawerVisible}
                    onFormCancel={() => setState({isDrawerVisible: false})}
                    drawerWidth={DrawerWidth.LARGE}
                    formToRender={formToRender()}
                />
            )
        )
    }

    const formToRender = () => {
        const {drawerType, recordIndex} = state
        const drawerFormState =
            drawerType === CreateOrUpdate.CREATE ? DrawerFormStateType.Create : DrawerFormStateType.Edit
        const procedureItemsSlot = drawerType === CreateOrUpdate.VIEW ? ProcedureViewItemsSlot : ProcedureFormItemsSlot

        return (
            <UIEngine
                {...editorConfig}
                formId={PROCEDURE_FORM_ID}
                config={config}
                initialValues={{
                    entity: props.dentalLoss,
                    drawerType,
                    recordIndex,
                    procedureType: props.transactionType,
                    procedureCodeList: props.procedureCodeList,
                    qualifierOptions: props.qualifierOptions
                }}
                validationRuleEngineConfiguration={{
                    serviceDeclarations: [DentalFormKrakenServiceDeclaration],
                    transformResultsDeclarations: []
                }}
                slotComponents={{
                    PROCEDURE_FORM: procedureItemsSlot
                }}
            >
                {drawerType === CreateOrUpdate.VIEW ? null : (
                    <>
                        <FormKrakenInitBlur />
                        <DrawerActions
                            handleFormCancel={closeDrawer}
                            drawerFormState={drawerFormState}
                            labels={{
                                editButtonLabel: t('dental-product:save')
                            }}
                            handleFormConfirm={handlerConfirm}
                            isKrakenValidation
                        />
                    </>
                )}
            </UIEngine>
        )
    }

    const handlerConfirm = (form: FormApi, kraken?: any, rulesEvaluationProps?: RulesEvaluationProps) => {
        if (form.getState().hasValidationErrors) {
            return
        }

        const {recordIndex} = state
        const {entity} = form.getState().values
        const curSubmittedProcedure = entity.lossDetail.submittedProcedures[recordIndex]
        const procedureDuplicate = checkProcedureDuplicate({item: curSubmittedProcedure, index: -1})

        const diagnosis = curSubmittedProcedure.diagnosisCodes
        const diagnosisDuplicate = checkDiagnosisDuplicate(diagnosis)

        if (procedureDuplicate || diagnosisDuplicate) {
            intakeWizardStore.setHasDuplicateData(true)
            return
        }

        kraken.evaluate({
            ...rulesEvaluationProps?.payload,
            onSuccess: () => {
                const {dentalLoss} = props
                let orgSubmittedProcedures = dentalLoss.lossDetail?.submittedProcedures || []
                orgSubmittedProcedures = orgSubmittedProcedures.map(procedure =>
                    procedure._key.id === curSubmittedProcedure._key.id ? curSubmittedProcedure : procedure
                )
                props.updateFormState(SUBMITTED_PROCEDURES_URI_PATH, orgSubmittedProcedures)
                closeDrawer()
                intakeWizardStore.setHasDuplicateData(false)
            },
            onFailure: () => {
                console.debug('Rules execution failed')
            }
        })
    }

    const closeDrawer = () => {
        setState({
            isDrawerVisible: false
        })
        props.formDrawerProps.closeDrawer()
        intakeWizardStore.setHasDuplicateData(false)
    }

    const checkDiagnosisDuplicate = diagnosis => {
        const uniqueDiagnosis = diagnosis.reduce((unique, item) => {
            if (!unique.some(obj => obj.qualifier === item.qualifier && obj.code === item.code)) {
                unique.push(item)
            }
            return unique
        }, [])

        return uniqueDiagnosis.length !== diagnosis.length
    }

    const checkProcedureDuplicate = data => {
        const {procedures} = props
        return procedures.find(
            procedure =>
                procedure._key.id !== data.item._key.id &&
                procedure.procedureCode === data.item.procedureCode &&
                procedure.dateOfService === data.item.dateOfService
        )
    }

    const recordValidation = data => {
        const duplicate = checkProcedureDuplicate(data)
        if (duplicate) {
            notification.error({
                message: t('dental-product:dental-claim-edit-row-is-duplicate')
            })
            return true
        }
        return false
    }

    const {procedures, createNewProduceEntity, isLoading, listActionMode, transactionType, receiveDate} = props
    return (
        <div className={PROCEDURES_TABLE}>
            <PrefixProvider stopPropagation>
                <EditableTable<CapDentalProcedureEntity, Date>
                    krakenValidationBeforeSubmit
                    items={procedures}
                    name='entity.lossDetail.submittedProcedures'
                    getColumns={
                        transactionType === TransactionType.ORTHODONTIC_SERVICES
                            ? (isEditable, getEntityInfo, extValue) =>
                                  getColumns(
                                      isEditable,
                                      getEntityInfo,
                                      props.procedureCodeList,
                                      mode,
                                      transactionType,
                                      extValue
                                  )
                            : (isEditable, getEntityInfo, extValue) =>
                                  getDefaultColumns(
                                      isEditable,
                                      getEntityInfo,
                                      props.procedureCodeList,
                                      transactionType as TransactionType,
                                      extValue
                                  )
                    }
                    createNewEntity={createNewProduceEntity}
                    recordValidation={recordValidation}
                    getEntityKey={getProceduresItemEntityKey}
                    labels={{
                        addButtonTitle: t('dental-product:dental-claim-claim-details-services-add-button'),
                        ...props.labels
                    }}
                    isMinorTable={props.isMinorTable}
                    isLoading={isLoading}
                    customAreaUnderTitle={props.customAreaUnderTitle}
                    listActionMode={listActionMode}
                    customButton={actionMenu}
                    isAddButtonDisabled={
                        [TransactionType.ORTHODONTIC_SERVICES, TransactionType.PREDETERMINATION_ORTHODONTICS].includes(
                            transactionType as TransactionType
                        ) && procedures?.length > 0
                    }
                    extValue={receiveDate}
                />
            </PrefixProvider>

            {renderDrawer()}
        </div>
    )
}
