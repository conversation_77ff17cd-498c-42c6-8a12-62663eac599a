yarn run v1.22.22
$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/products/dental-product[39m

 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 24[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/utils.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 32[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/accumulatorTypeUtils.test.tsx [2m([22m[2m14 tests[22m[2m)[22m[90m 22[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/DentalEditPageRootStore.test.tsx [2m([22m[2m17 tests[22m[2m)[22m[90m 41[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/WithinFormEditWrapperStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 71[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/store/PolicyAndPatientStore.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 37[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/DentalEntryPage.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[33m 521[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[33m 646[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/DentalEditPage.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 71[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/DentalOverviewPageStore.test.tsx [2m([22m[2m22 tests[22m[2m)[22m[90m 54[2mms[22m[39m
 [32m✓[39m test/pages/dental-adjust-page-deprecated/store/DentalAdjustPageStore.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[90m 42[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/store/DentalEntryPageStore.test.tsx [2m([22m[2m15 tests[22m[2m)[22m[90m 36[2mms[22m[39m
 [31m❯[39m test/shared/common/claim-procedures/column-renderers.test.tsx [2m([22m[2m0 test[22m[2m)[22m
 [32m✓[39m test/pages/dental-entry-page/components/dental-claims-table/DentalClaimsTable.test.tsx [2m([22m[2m13 tests[22m[2m)[22m[90m 48[2mms[22m[39m
 [31m❯[39m test/shared/common/claim-procedures/utils.test.tsx [2m([22m[2m0 test[22m[2m)[22m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/AdjudicationResultsTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 213[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/paymentServiceUtils.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 35[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/PaymentActionsDropdown.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 554[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/service/useBalanceLogService.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 23[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/claim-info-tab/ClaimInfoTab.test.tsx [2m([22m[2m9 tests[22m[2m)[22m[33m 907[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/utils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 18[2mms[22m[39m
 [32m✓[39m test/utils/common/ValidationUtils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 9[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/ClaimPolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 197[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardRulesExecutionStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/utils/Utils.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 5[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/store/ProviderStore.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 108[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/UploadDocumentsStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/accumulator-tab/AccumutatorTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 298[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPayments.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[33m 528[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/AuthorityApproval.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 113[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/claim-procedures-view-items/ClaimProceduresViewItems.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 551[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentTableExpandRow.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 91[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/OverpaymentAmount.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 100[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 6[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/claim-provider-search-with-field/ClaimProviderSearchWithField.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 679[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceActivities.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 368[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/PolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 100[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/ClaimProviderSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 98[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/ProviderDetail.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 101[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/AdditionalInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 98[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/IntakeAndServices.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 243[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/UploadDocuments.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 88[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/UploaderFileItem.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 72[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethSelect.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 268[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 197[2mms[22m[39m
 [32m✓[39m test/shared/common/components/procedure-code-search/ProcedureCodeSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 78[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-intake/claim-intake-section/ClaimIntakeSectionSlot.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 20854[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimIntakeSectionSlot[2m > [22mshould export the component correctly [33m20852[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/ExpandedAdjudicationRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 50[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/comments-and-remarks/CommentsAndRemarks.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 75[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/additional-claim-info/AdditionalClaimInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 198[2mms[22m[39m
 [32m✓[39m test/shared/common/components/select-patient-card/SelectPatientCard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 52[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/AddMissingTeeth.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 197[2mms[22m[39m
 [32m✓[39m test/shared/common/components/contact-cards/ContactCardPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 31[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 26[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/Card.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 25[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardWrapper.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 21[2mms[22m[39m

[31m⎯⎯⎯⎯⎯⎯[1m[7m Failed Suites 2 [27m[22m⎯⎯⎯⎯⎯⎯⎯[39m

[31m[1m[7m FAIL [27m[22m[39m test/shared/common/claim-procedures/column-renderers.test.tsx[2m [ test/shared/common/claim-procedures/column-renderers.test.tsx ][22m
[31m[1mError[22m: [vitest] No "Localization" export is defined on the "@eisgroup/i18n" mock. Did you forget to return it from "vi.mock"?
If you need to partially mock a module, you can use "importOriginal" helper inside:
[39m
vi[33m.[39m[34mmock[39m([35mimport[39m([32m"@eisgroup/i18n"[39m)[33m,[39m [35masync[39m (importOriginal) [33m=>[39m {
  [35mconst[39m actual [33m=[39m [35mawait[39m [34mimportOriginal[39m()
  [35mreturn[39m {
    [33m...[39mactual[33m,[39m
    [90m// your mocked methods[39m
  }
})

[36m [2m❯[22m test/support/TestUtils.tsx:[2m27:5[22m[39m
    [90m 25| [39m
    [90m 26| [39m[35mexport[39m [35mconst[39m initLocalization [33m=[39m [33mIoC[39m[33m.[39m[35mget[39m[33m<[39m[33mLocalization[39m[33m.[39m[33mLocalizerProvider[39m…
    [90m 27| [39m    [33mLocalization[39m[33m.[39m[33mTYPES[39m[33m.[39m[33mLocalizerProvider[39m
    [90m   | [39m    [31m^[39m
    [90m 28| [39m)[33m.[39m[34minitLocalizer[39m({
    [90m 29| [39m    supportedLocales[33m:[39m [mockEnLocale][33m,[39m
[90m [2m❯[22m test/shared/common/claim-procedures/column-renderers.test.tsx:[2m54:25[22m[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/2]⎯[22m[39m

[31m[1m[7m FAIL [27m[22m[39m test/shared/common/claim-procedures/utils.test.tsx[2m [ test/shared/common/claim-procedures/utils.test.tsx ][22m
[31m[1mError[22m: [vitest] No "LocalizationUtils" export is defined on the "@eisgroup/i18n" mock. Did you forget to return it from "vi.mock"?
[2m Test Files [22m [1m[31m2 failed[39m[22m[2m | [22m[1m[32m56 passed[39m[22m[90m (58)[39m
If you need to partially mock a module, you can use "importOriginal" helper inside:
[2m      Tests [22m [1m[32m334 passed[39m[22m[90m (334)[39m
[2m   Start at [22m 15:04:11
[2m   Duration [22m 86.99s[2m (transform 3.91s, setup 164.15s, collect 901.05s, tests 29.37s, environment 74.45s, prepare 11.32s)[22m

[39m
vi[33m.[39m[34mmock[39m([35mimport[39m([32m"@eisgroup/i18n"[39m)[33m,[39m [35masync[39m (importOriginal) [33m=>[39m {
  [35mconst[39m actual [33m=[39m [35mawait[39m [34mimportOriginal[39m()
  [35mreturn[39m {
    [33m...[39mactual[33m,[39m
    [90m// your mocked methods[39m
  }
})

[36m [2m❯[22m src/utils/common/utils.tsx:[2m13:17[22m[39m
    [90m 11| [39m[35mimport[39m {[33mLocalizationUtils[39m} [35mfrom[39m [32m'@eisgroup/i18n'[39m
    [90m 12| [39m
    [90m 13| [39m[35mimport[39m locale [33m=[39m [33mLocalizationUtils[39m[33m.[39mlocale
    [90m   | [39m                [31m^[39m
    [90m 14| [39m[35mimport[39m localeToString [33m=[39m [33mLocalizationUtils[39m[33m.[39mlocaleToString
    [90m 15| [39m
[90m [2m❯[22m src/utils/common/ValidationUtils.ts:[2m3:31[22m[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/2]⎯[22m[39m

error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
