$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/products/dental-product[39m

 [32m✓[39m test/pages/dental-overview-page/store/accumulatorTypeUtils.test.tsx [2m([22m[2m14 tests[22m[2m)[22m[90m 32[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/utils.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 28[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/DentalEditPageRootStore.test.tsx [2m([22m[2m17 tests[22m[2m)[22m[90m 39[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/WithinFormEditWrapperStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 63[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/store/PolicyAndPatientStore.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 35[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/store/ProviderStore.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/paymentServiceUtils.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/DentalOverviewPageStore.test.tsx [2m([22m[2m22 tests[22m[2m)[22m[90m 123[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/service/useBalanceLogService.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 23[2mms[22m[39m
 [32m✓[39m test/pages/dental-adjust-page-deprecated/store/DentalAdjustPageStore.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[90m 45[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/store/DentalEntryPageStore.test.tsx [2m([22m[2m15 tests[22m[2m)[22m[90m 39[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/utils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/DentalEntryPage.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[33m 558[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/claim-info-tab/ClaimInfoTab.test.tsx [2m([22m[2m9 tests[22m[2m)[22m[33m 874[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/components/dental-claims-table/DentalClaimsTable.test.tsx [2m([22m[2m13 tests[22m[2m)[22m[90m 50[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[33m 730[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/DentalEditPage.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 84[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/PaymentActionsDropdown.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 584[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/AdjudicationResultsTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 219[2mms[22m[39m
 [32m✓[39m test/utils/common/ValidationUtils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 9[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/utils.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 131[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPayments.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[33m 644[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/UploadDocumentsStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/AuthorityApproval.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 115[2mms[22m[39m
 [31m❯[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand.test.tsx [2m([22m[2m4 tests[22m[2m | [22m[31m1 failed[39m[2m)[22m[90m 155[2mms[22m[39m
[31m   [31m×[31m RecalculatedPaymentExpand[2m > [22mshould display correct payment and allocation amounts[90m 36[2mms[22m[31m[39m
[31m     → Found multiple elements with the text: $100.00

Here are the matching elements:

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).

[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"ant-row gen-recalculated-payments-allocation-table-expand-row"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8"[31m
        [36m>[31m
          [36m<h4>[31m
            [0mD1234-Test Procedure(08/07/2025)[0m
          [36m</h4>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8 gen-recalculated-payments-allocation-table-expand-payment-amount"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-allocation[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-interests[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8 gen-recalculated-payments-allocation-table-expand-should-have-paid"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-allocation[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$50.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-interests[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
        [36m</div>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
 [32m✓[39m test/pages/dental-overview-page/components/accumulator-tab/AccumutatorTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 357[2mms[22m[39m
   [33m[2m✓[22m[39m AccumulatorTab[2m > [22mBasic Rendering[2m > [22mshould render card with correct title and policy date field [33m354[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/ClaimPolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 220[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/claim-procedures-view-items/ClaimProceduresViewItems.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 562[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardRulesExecutionStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/utils/Utils.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 9[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 5[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/OverpaymentAmount.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 66[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentTableExpandRow.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 75[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/PolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 99[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceActivities.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 375[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/claim-provider-search-with-field/ClaimProviderSearchWithField.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 670[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/ClaimProviderSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 124[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/AdditionalInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 106[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/UploadDocuments.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 142[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/ProviderDetail.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 110[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/UploaderFileItem.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 93[2mms[22m[39m
 [32m✓[39m test/shared/common/components/procedure-code-search/ProcedureCodeSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 114[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethSelect.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 372[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/IntakeAndServices.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 235[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-intake/claim-intake-section/ClaimIntakeSectionSlot.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 20186[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimIntakeSectionSlot[2m > [22mshould export the component correctly [33m20184[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 286[2mms[22m[39m
 [32m✓[39m test/shared/common/components/select-patient-card/SelectPatientCard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 58[2mms[22m[39m
 [32m✓[39m test/shared/common/components/contact-cards/ContactCardPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 33[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/ExpandedAdjudicationRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 36[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/AddMissingTeeth.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 150[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/additional-claim-info/AdditionalClaimInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 136[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 26[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/comments-and-remarks/CommentsAndRemarks.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 38[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 22[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardWrapper.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/Card.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 22[2mms[22m[39m

[31m⎯⎯⎯⎯⎯⎯⎯[1m[7m Failed Tests 1 [27m[22m⎯⎯⎯⎯⎯⎯⎯[39m

[31m[1m[7m FAIL [27m[22m[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand.test.tsx[2m > [22mRecalculatedPaymentExpand[2m > [22mshould display correct payment and allocation amounts
[31m[1mTestingLibraryElementError[22m: Found multiple elements with the text: $100.00

Here are the matching elements:

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

[36m<span>[31m
  [0m$100.00[0m
[36m</span>[31m

(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).

[36m<body>[31m
  [36m<div>[31m
    [36m<div[31m
      [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand"[31m
    [36m>[31m
      [36m<div[31m
        [33mclass[31m=[32m"ant-row gen-recalculated-payments-allocation-table-expand-row"[31m
      [36m>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8"[31m
        [36m>[31m
          [36m<h4>[31m
            [0mD1234-Test Procedure(08/07/2025)[0m
          [36m</h4>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8 gen-recalculated-payments-allocation-table-expand-payment-amount"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-allocation[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-interests[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
        [36m</div>[31m
        [36m<div[31m
          [33mclass[31m=[32m"ant-col ant-col-8 gen-recalculated-payments-allocation-table-expand-should-have-paid"[31m
        [36m>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-allocation[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$50.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
          [36m<div[31m
            [33mclass[31m=[32m"gen-recalculated-payments-allocation-table-expand-allocation"[31m
          [36m>[31m
            [36m<label>[31m
              [0mdental-product:dental-claim-balance-balance-recalculated-payments-total-interests[0m
            [36m</label>[31m
            [36m<span>[31m
              [36m<span>[31m
                [0m$100.00[0m
              [36m</span>[31m
            [36m</span>[31m
          [36m</div>[31m
        [36m</div>[31m
      [36m</div>[31m
    [36m</div>[31m
  [36m</div>[31m
[36m</body>[31m[39m
[90m [2m❯[22m Object.getElementError ../../node_modules/@testing-library/dom/dist/config.js:[2m37:19[22m[39m
[90m [2m❯[22m getElementError ../../node_modules/@testing-library/dom/dist/query-helpers.js:[2m25:35[22m[39m
[90m [2m❯[22m getMultipleElementsFoundError ../../node_modules/@testing-library/dom/dist/query-helpers.js:[2m29:10[22m[39m
[90m [2m❯[22m ../../node_modules/@testing-library/dom/dist/query-helpers.js:[2m66:13[22m[39m
[90m [2m❯[22m getByText ../../node_modules/@testing-library/dom/dist/query-helpers.js:[2m111:19[22m[39m
[36m [2m❯[22m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand.test.tsx:[2m74:23[22m[39m
    [90m 72| [39m        [34mrender[39m([33m<[39m[33mRecalculatedPaymentExpand[39m [33mbalanceItem[39m[33m=[39m[33m{[39mmockBalanceItem…
    [90m 73| [39m
    [90m 74| [39m        [34mexpect[39m(screen[33m.[39m[34mgetByText[39m([32m'$100.00'[39m))[33m.[39m[34mtoBeInTheDocument[39m()
    [90m   | [39m                      [31m^[39m
    [90m 75| [39m    })
    [90m 76| [39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/1]⎯[22m[39m

[2m Test Files [22m [1m[31m1 failed[39m[22m[2m | [22m[1m[32m56 passed[39m[22m[90m (57)[39m
[2m      Tests [22m [1m[31m1 failed[39m[22m[2m | [22m[1m[32m338 passed[39m[22m[90m (339)[39m
[2m   Start at [22m 17:55:48
[2m   Duration [22m 132.09s[2m (transform 4.18s, setup 160.34s, collect 1591.52s, tests 29.41s, environment 72.20s, prepare 10.32s)[22m

error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
