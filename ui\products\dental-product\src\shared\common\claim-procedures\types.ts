/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'

export interface ClaimProceduresItemsControls {
    preauthorizationNumber: React.ReactNode
    dateOfService: React.ReactNode
    procedureCode: React.ReactNode
    quantity: React.ReactNode
    submittedFee: React.ReactNode
    paymentInfo?: React.ReactNode
    toothArea: React.ReactNode
    surfaces: React.ReactNode
    toothSystem: React.ReactNode
    toothCodes: React.ReactNode
    diagnosis: React.ReactNode
    dividerOrthDetails: React.ReactNode
    orthoMonthQuantity: React.ReactNode
    appliancePlacedDate: React.ReactNode
    orthoFrequencyCd: React.ReactNode
    downPayment: React.ReactNode
    dividerProsthesisDetails: React.ReactNode
    priorProsthesisPlacementDate: React.ReactNode
}
