package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import org.junit.Test;

import java.lang.reflect.Constructor;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class CapDentalLossCommandsTest {

    @Test
    public void testConstantsHaveCorrectValues() {
        assertEquals("openLoss", CapDentalLossCommands.OPEN_LOSS);
        assertEquals("pendLoss", CapDentalLossCommands.PEND_LOSS);
        assertEquals("suspendLoss", CapDentalLossCommands.SUSPEND_LOSS);
        assertEquals("unsuspendLoss", CapDentalLossCommands.UNSUSPEND_LOSS);
        assertEquals("requestCloseLoss", CapDentalLossCommands.REQUEST_CLOSE_LOSS);
    }

    @Test
    public void testConstructorIsPrivate() throws Exception {
        Constructor<CapDentalLossCommands> constructor = CapDentalLossCommands.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        assertNotNull(constructor.newInstance());
    }
}
