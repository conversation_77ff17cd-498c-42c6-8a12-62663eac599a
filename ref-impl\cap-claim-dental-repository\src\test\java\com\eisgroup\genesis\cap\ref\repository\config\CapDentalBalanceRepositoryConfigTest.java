/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepository;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalBalanceRepositoryConfigTest {

    @InjectMocks
    private CapDentalBalanceRepositoryConfig config;

    @Mock
    private ModeledTransformationService modeledTransformationService;

    @Mock
    private ColumnStore columnStore;
    @Mock
    private StatementBuilderFactory statementFactory;

    @Test
    public void shouldCapDentalBalanceRepository() {
        CapDentalBalanceRepository result = config.capDentalBalanceRepository(columnStore, statementFactory);
        assertThat(result, notNullValue());
    }

}