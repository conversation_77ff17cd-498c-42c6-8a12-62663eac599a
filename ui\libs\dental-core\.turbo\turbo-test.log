$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/libs/dental-core[39m

 [32m✓[39m test/components/money-format/MoneyFormat.test.tsx [2m([22m[2m11 tests[22m[2m)[22m[90m 62[2mms[22m[39m
 [32m✓[39m test/components/claim-toggle/ClaimToggle.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 35[2mms[22m[39m
 [32m✓[39m test/components/multi-steps/index.test.tsx [2m([22m[2m10 tests[22m[2m)[22m[33m 316[2mms[22m[39m

[2m Test Files [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m      Tests [22m [1m[32m22 passed[39m[22m[90m (22)[39m
[2m   Start at [22m 17:46:42
[2m   Duration [22m 531.42s[2m (transform 10.67s, setup 230.89s, collect 591.94s, tests 412ms, environment 451.43s, prepare 15.49s)[22m

