/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.context.annotation.Bean;

/**
 * Claim Dental Settlement repository configuration.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalSettlementRepositoryConfig {

    @Bean
    public CapDentalSettlementIndexResolver capDentalSettlementIndexResolver(
            ModeledTransformationService modeledTransformationService) {
        return new CapDentalSettlementIndexResolver(modeledTransformationService, ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }

}
