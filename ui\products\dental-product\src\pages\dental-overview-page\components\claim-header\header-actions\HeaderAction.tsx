import React from 'react'

import {useTranslate} from '@eisgroup/i18n'

import {DrawerFormStateType, useFormDrawerAction} from '../../../../../shared/common'
import {HeaderDropdownMenu} from '../../../../../shared/components'
import {useStore} from '../../../store/DentalOverviewPageStore'
import {ChangeClaimSubstatusDrawer} from '../change-claim-substatus-drawer/ChangeClaimSubstatusDrawer'
import {DentalHeaderActionDrawerKey} from '../ClaimHeader'
import {CommonWarningAction} from './components'
import {useCommonWarningAction, useHeaderActions} from './hooks'

export const HeaderAction = () => {
    const {loss, suspendDentalLoss, unsuspendDentalLoss} = useStore()
    const {t} = useTranslate()
    const {openDrawer, openedDrawerKey, closeDrawer} = useFormDrawerAction()
    const {getActionItems, onSelectAction} = useHeaderActions(loss, openDrawer)

    const {loading: suspendLoading, onConfirmAction: confirmSuspend} = useCommonWarningAction(
        suspendDentalLoss,
        closeDrawer
    )
    const {loading: unsuspendLoading, onConfirmAction: confirmUnsuspend} = useCommonWarningAction(
        unsuspendDentalLoss,
        closeDrawer
    )
    const items = getActionItems()
    const disabledDropdown = Object.keys(items)
        .map(key => items[key].isHidden)
        .every(hidden => hidden)
    return (
        <>
            <HeaderDropdownMenu
                disabled={disabledDropdown}
                actions={items}
                onSelectAction={onSelectAction}
                label={t('dental-product:dental-header-claim-claim-actions')}
            />
            {openedDrawerKey === DentalHeaderActionDrawerKey.CHANGE_SUBSTATUS_DRAWER_KEY ? (
                <ChangeClaimSubstatusDrawer
                    title={t('dental-product:dental-header-claim-action-change-claim-substatus')}
                    drawerFormType={DrawerFormStateType.Edit}
                    closeDrawer={closeDrawer}
                />
            ) : null}
            {openedDrawerKey === DentalHeaderActionDrawerKey.CONFIRM_SUSPEND_CLAIM_MODAL_KEY ? (
                <CommonWarningAction
                    visible
                    onClose={closeDrawer}
                    onOk={confirmSuspend}
                    comfirmLoading={suspendLoading}
                    title={t('dental-product:dental-header-claim-action-suspend-claim-modal-title')}
                    content={t('dental-product:dental-header-claim-action-suspend-claim-modal-content')}
                />
            ) : null}
            {openedDrawerKey === DentalHeaderActionDrawerKey.CONFIRM_RECOVERY_CLAIM_MODAL_KEY ? (
                <CommonWarningAction
                    visible
                    onClose={closeDrawer}
                    onOk={confirmUnsuspend}
                    comfirmLoading={unsuspendLoading}
                    title={t('dental-product:dental-header-claim-action-recovery-claim-modal-title')}
                    content={t('dental-product:dental-header-claim-action-recovery-claim-modal-content')}
                />
            ) : null}
        </>
    )
}
