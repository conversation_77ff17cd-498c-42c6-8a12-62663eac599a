/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'

import {GetColumnsFn} from '@eisgroup/dental-core'
import {CapDentalLoss} from '@eisgroup/dental-models'
import {Field, LookupValue, PrefixProvider} from '@eisgroup/form'
import {t} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Col, Row} from '@eisgroup/ui-kit'

import {CLAIM_MODE, CLAIM_MODE_TYPE} from '../../../pages/dental-edit-page/constants'
import {TransactionType} from '../../../utils/common/constants'
import {CLAIM_PROCEDURE_EXT_SECTION_ORTHODONTICS, CLAIM_PROCEDURE_FORM_ITEM_ROW} from './classnames'
import {
    renderDateOfService,
    renderDiagnosisCode,
    renderDiagnosisQualifier,
    renderProcedureCode,
    renderQuantity,
    renderSubmittedFee
} from './column-renderers'
import {ClaimProceduresItemsControls} from './types'
import {createValidators} from './validators'

import CapDentalProcedureEntity = CapDentalLoss.CapDentalProcedureEntity
import CapDentalDiagnosisCodeEntity = CapDentalLoss.CapDentalDiagnosisCodeEntity

const {InputNumber, LookupSelect} = Field

export const getDefaultColumns: (
    isEditable: (entity: CapDentalProcedureEntity) => boolean,
    getEntityInfo,
    procedureCodeList: LookupValue[],
    transactionType: TransactionType,
    receiveDate?: Date
) => ReturnType<GetColumnsFn<CapDentalProcedureEntity, Date>> = (
    isEditable,
    getEntityInfo,
    procedureCodeList,
    transactionType,
    receiveDate
) => {
    const dateOfServiceRequired = [TransactionType.ACTUAL_SERVICES, TransactionType.ORTHODONTIC_SERVICES].includes(
        transactionType
    )

    return [
        {
            title: t('dental-product:dental-claim-claim-details-services-line'),
            key: 'line',
            editable: false,
            render: (text, entity: CapDentalProcedureEntity, index: number) => {
                const serviceIndex = (index + 1).toString()
                return index + 1 < 10 ? `0${serviceIndex}` : serviceIndex
            }
        },
        {
            title: t('dental-product:dental-claim-claim-details-services-date-of-service'),
            key: 'dateOfService',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderDateOfService(entity, isEditable(entity), prefixName, dateOfServiceRequired, receiveDate)
            }
        },
        {
            title: t('dental-product:dental-claim-claim-details-services-procedure-code'),
            key: 'procedureCode',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderProcedureCode(entity, isEditable(entity), prefixName, procedureCodeList)
            }
        },
        {
            title: t('dental-product:dental-claim-claim-details-services-quantity'),
            key: 'quantity',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderQuantity(entity, isEditable(entity), prefixName)
            }
        },
        {
            title: t('dental-product:dental-claim-claim-details-services-charges'),
            key: 'submittedFee',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderSubmittedFee(entity, isEditable(entity), prefixName)
            }
        }
    ]
}

export const getColumns: (
    isEditable: (entity: CapDentalProcedureEntity) => boolean,
    getEntityInfo,
    procedureCodeList: LookupValue[],
    mode: CLAIM_MODE_TYPE,
    transactionType: TransactionType,
    receiveDate?: Date
) => ReturnType<GetColumnsFn<CapDentalProcedureEntity, Date>> = (
    isEditable,
    getEntityInfo,
    procedureCodeList,
    mode,
    transactionType,
    receiveDate
) => {
    return [
        ...getDefaultColumns(isEditable, getEntityInfo, procedureCodeList, transactionType, receiveDate),
        {
            title: t('dental-product:dental-claim-claim-details-services-payment-frequency'),
            key: 'ortho.orthoFrequencyCd',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return isEditable(entity) ? (
                    <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
                        <LookupSelect
                            name={`${prefixName}.ortho.orthoFrequencyCd`}
                            lookupName='CapDNFrequency'
                            required
                            disabled={mode === CLAIM_MODE.ADJUST}
                            validate={createValidators.paymentFrequency()}
                        />
                    </div>
                ) : (
                    <LookupLabel
                        lookup='CapDNFrequency'
                        code={entity.ortho?.orthoFrequencyCd}
                        emptyLabel={t('dental-product:empty')}
                    />
                )
            }
        },
        {
            title: t('dental-product:dental-claim-claim-details-services-months-of-treatment'),
            key: 'ortho.orthoMonthQuantity',
            editable: true,
            render: (entity: CapDentalProcedureEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return isEditable(entity) ? (
                    <div className={CLAIM_PROCEDURE_FORM_ITEM_ROW}>
                        <InputNumber
                            name={`${prefixName}.ortho.orthoMonthQuantity`}
                            required
                            validate={createValidators.monthsOfTreatment()}
                        />
                    </div>
                ) : (
                    entity.ortho?.orthoMonthQuantity
                )
            }
        }
    ]
}

export const getDiagnosisColumns: (
    isEditable: (entity: CapDentalDiagnosisCodeEntity) => boolean,
    getEntityInfo,
    qualifierOptions: LookupValue[]
) => ReturnType<GetColumnsFn<CapDentalDiagnosisCodeEntity, undefined>> = (
    isEditable,
    getEntityInfo,
    qualifierOptions
) => {
    return [
        {
            title: t('dental-product:dental-claim-edit-procedure-details-diagnosis-qualifier'),
            key: 'qualifier',
            editable: true,
            render: (entity: CapDentalDiagnosisCodeEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderDiagnosisQualifier(entity, isEditable(entity), prefixName, qualifierOptions)
            }
        },
        {
            title: t('dental-product:dental-claim-edit-procedure-details-diagnosis-code'),
            key: 'code',
            editable: true,
            render: (entity: CapDentalDiagnosisCodeEntity) => {
                const {prefixName} = getEntityInfo(entity)
                return renderDiagnosisCode(entity, isEditable(entity), prefixName)
            }
        }
    ]
}

export const getDiagnosisViewColumns = (qualifierOptions: LookupValue[]) => {
    return [
        {
            title: t('dental-product:dental-claim-edit-procedure-details-diagnosis-qualifier'),
            key: 'qualifier',
            editable: true,
            render: (entity: CapDentalDiagnosisCodeEntity) =>
                qualifierOptions.find(v => v.code === entity.qualifier)?.displayValue ?? t('dental-product:empty')
        },
        {
            title: t('dental-product:dental-claim-edit-procedure-details-diagnosis-code'),
            key: 'code',
            editable: true,
            render: (entity: CapDentalDiagnosisCodeEntity) => entity.code ?? t('dental-product:empty')
        }
    ]
}

export const renderContent = (
    controls: ClaimProceduresItemsControls,
    recordIndex: number,
    procedureType: string
): React.ReactNode => {
    return (
        <PrefixProvider prefixInputName={`entity.lossDetail.submittedProcedures[${recordIndex}]`}>
            <Row gutter={24}>
                <Col span={12}>{controls.preauthorizationNumber}</Col>
            </Row>
            <Row gutter={24}>
                <Col span={12}>{controls.dateOfService}</Col>
                <Col span={12}>{controls.procedureCode}</Col>
            </Row>
            <Row gutter={24}>
                <Col span={12}>{controls.quantity}</Col>
                <Col span={12}>{controls.submittedFee}</Col>
            </Row>
            {controls.paymentInfo && (
                <Row gutter={24}>
                    <Col span={24}>{controls.paymentInfo}</Col>
                </Row>
            )}
            <Row gutter={24}>
                <Col span={12}>{controls.toothArea}</Col>
                <Col span={12}>{controls.surfaces}</Col>
            </Row>
            <Row gutter={24}>
                <Col span={12}>{controls.toothSystem}</Col>
                <Col span={12}>{controls.toothCodes}</Col>
            </Row>
            <Row gutter={24}>
                <Col>{controls.diagnosis}</Col>
            </Row>
            {procedureType === TransactionType.ORTHODONTIC_SERVICES && (
                <div className={CLAIM_PROCEDURE_EXT_SECTION_ORTHODONTICS}>
                    <div>{controls.dividerOrthDetails}</div>
                    <Row gutter={24}>
                        <Col span={12}>{controls.orthoMonthQuantity}</Col>
                        <Col span={12}>{controls.appliancePlacedDate}</Col>
                    </Row>
                    <Row gutter={24}>
                        <Col span={12}>{controls.downPayment}</Col>
                        <Col span={12}>{controls.orthoFrequencyCd}</Col>
                    </Row>
                    <div>{controls.dividerProsthesisDetails}</div>
                    <Row gutter={24}>
                        <Col span={12}>{controls.priorProsthesisPlacementDate}</Col>
                    </Row>
                </div>
            )}
        </PrefixProvider>
    )
}
