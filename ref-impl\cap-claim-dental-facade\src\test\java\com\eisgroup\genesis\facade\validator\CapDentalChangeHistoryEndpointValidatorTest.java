/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade.validator;

import com.eisgroup.genesis.cap.versioning.facade.CapChangeHistoryRequest;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.facade.FacadeFailureException;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static com.eisgroup.genesis.facade.validator.CapDentalChangeHistoryEndpointValidator.CapDentalChangeHistoryDefinition.URI_REQUIRED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

/**
 *
 * <AUTHOR>
 * @since 25.200
 */
@RunWith(MockitoJUnitRunner.class)
public class CapDentalChangeHistoryEndpointValidatorTest {

    @InjectMocks
    private CapDentalChangeHistoryEndpointValidator validator;
    @Mock
    private CapChangeHistoryRequest request;

    @Test
    public void testValidationURIRequired() {

        TestStreamable.create(validator.validateRequest(request))
                //then
                .assertError(FacadeFailureException.class)
                .assertError(err -> {
                    List<ErrorHolder> childErrors = new ArrayList<>(((FacadeFailureException) err).getErrorHolder().getErrors());
                    assertThat(childErrors, hasSize(1));
                    assertThat(childErrors.getFirst().getCode(), equalTo(URI_REQUIRED.getCode()));
                    return true;
                });
    }
}
