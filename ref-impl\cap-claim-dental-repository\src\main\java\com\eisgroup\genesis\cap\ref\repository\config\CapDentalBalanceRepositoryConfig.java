/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepositoryImpl;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import org.springframework.context.annotation.Bean;

/**
 * Contains beans related to dental balance repository.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalBalanceRepositoryConfig {

    @Bean
    public CapDentalBalanceRepository capDentalBalanceRepository(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        return new CapDentalBalanceRepositoryImpl(columnStore, statementFactory);
    }
}
