/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.entity.metadata.config.EntitySchemaConfiguration;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Entity schema configuration for Dental Payments
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalFinancialEntitySchemaConfiguration implements EntitySchemaConfiguration {

    private static final ModelRepository<DomainModel> modelRepository = ModelRepositoryFactory
            .getRepositoryFor(DomainModel.class);

    @Override
    public Collection<String> getAppliesTo() {
        return modelRepository.getAllModels().stream().map(modelRepository::getActiveModel)
                .filter(model -> "CapPayment".equals(model.getModelType()))
                .flatMap(model -> getSchemaName(model).stream())
                .collect(Collectors.toList());
    }

    @Override
    public Collection<Class<?>> getScanClassesNames() {
        return List.of();
    }

    private Collection<String> getSchemaName(DomainModel model) {
        return List.of(ModeledEntitySchemaResolver.getSchemaNameUsing(model, PaymentVariations.PAYMENT));
    }
}