/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.inpt;

import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementUpdateInput;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementDetailEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.gson.GsonFactory;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.common.io.Resources;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.Assert.assertNotNull;

public class CapDentalSettlementUpdateInputTest {

    private CapDentalSettlementUpdateInput input;

    @Before
    public void setUp() throws IOException {
        var LifeSettlementInitInput = Resources.toString(Resources.getResource("json/capDentalSettlement/updateDentalSettlementInput.json"), StandardCharsets.UTF_8);
        JsonObject jsonObject = GsonFactory.gson().fromJson(LifeSettlementInitInput, JsonObject.class);
        input = new CapDentalSettlementUpdateInput(jsonObject);
    }

    @Test
    public void shouldReturnEntity() {
        // when
        CapDentalSettlementDetailEntity entity = input.getEntity();

        // then
        assertNotNull(entity);
    }

    @Test
    public void shouldReturnClaimWrapperIdentification() {
        // when
        EntityLink<RootEntity> result = input.getClaimLossIdentification();

        // then
        assertNotNull(result.getURI());
    }
}