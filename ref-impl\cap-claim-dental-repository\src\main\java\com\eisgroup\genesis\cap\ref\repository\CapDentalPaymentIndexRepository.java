/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.model.capdentalpaymentindex.CapDentalPaymentIdxEntity;

import java.util.List;

/**
 * {@link CapDentalPaymentIdxEntity} entities resolver.
 *
 * <AUTHOR>
 * @since 25.10
 */

public interface CapDentalPaymentIndexRepository {

    /**
     * Loads {@link CapDentalPaymentIdxEntity} entity by variation and provided state
     *
     * @return loaded {@link CapDentalPaymentIdxEntity}
     */
    Streamable<List<CapDentalPaymentIdxEntity>> loadPaymentIndexesByState(String applicableState);
}
