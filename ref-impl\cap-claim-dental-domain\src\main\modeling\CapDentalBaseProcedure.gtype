BaseType CapDentalBaseProcedure {

    Attr cob: CapDentalProcedureCoordinationOfBenefitsEntity

    @Description("Date of Service.")
    @DateOfLoss
    Attr dateOfService: Date

    @Description("Procedure Description.")
    @NonComparable
    Attr description: String

    Attr diagnosisCodes: *CapDentalDiagnosisCodeEntity

    @KrakenChildContext
    @KrakenField
    Attr ortho: CapDentalOrthodonticEntity

    Attr preauthorization: CapDentalPreauthorizationEntity

    @Description("Preauthorization Number.")
    Attr preauthorizationNumber: String

    @Description("Is this procedure part of predetermination or real service?")
    Attr predetInd: Bo<PERSON>an

    @Description("Date of Prior Prosthesis Placement.")
    Attr priorProsthesisPlacementDate: Date

    @Lookup("CapDNProcedureCode")
    @Description("CDT Code.")
    Attr procedureCode: String

    @Description("Procedure Type.")
    @NonComparable
    Attr procedureType: String

    @Description("Number of services.")
    Attr quantity: Integer

    @Description("Amount Charged.")
    Attr submittedFee: Money

    @Description("Tooth Surface.")
    @Lookup("CapDNSurfaces")
    Attr surfaces: *String

    @Description("Area of Oral Cavity.")
    @Lookup("CapDNToothArea")
    Attr toothArea: String

    @Description("Tooth Numbers/Letters.")
    @Lookup("CapDNToothCodes")
    Attr toothCodes: *String

    @Description("Tooth System.")
    @Lookup("CapDNToothSystem")
    Attr toothSystem: String
}