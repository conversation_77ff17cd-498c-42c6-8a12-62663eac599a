/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.adjudication.command.CapReadjudicateSettlementHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementUpdateInput;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementValidator;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * The command that update settlement
 *
 * <AUTHOR>
 * @since 25.12
 */

@Modifying
public class CapDentalSettlementUpdateHandler extends CapReadjudicateSettlementHandler<CapDentalSettlementUpdateInput, CapSettlement> {

    @Autowired
    private CapDentalSettlementValidator capDentalSettlementValidator;


    @Override
    public Streamable<ErrorHolder> validateAsync(CapDentalSettlementUpdateInput input, CapSettlement loadedEntity) {
        return Streamable.concat(super.validateAsync(input, loadedEntity), capDentalSettlementValidator.validateLossNotClosed(input.getClaimLossIdentification()));
    }


    @Override
    public String getName() {
        return CapDentalSettlementCommands.UPDATE_SETTLEMENT;
    }

}
