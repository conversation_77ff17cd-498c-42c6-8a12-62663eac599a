/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link;

import java.util.UUID;

import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Dental patient history procedure link used to parse {@link EntityLink} and construct entity URI string.
 *
 * <AUTHOR>
 * @since 22.8
 */
public class ManualProcedureLink {

    public static final String ENTITY_URI_FORMAT = "capPatient://manualHistory/geroot://%s/%s/%s/%s";

    private final String type;
    private final String modelName;
    private final String modelType;
    private final UUID rootId;
    private final Integer version;

    public ManualProcedureLink(EntityLink<?> link) {
        this.type = link.getURI().getHost();
        String path = link.getURI().getPath();
        String[] pathParams = path.split(EntityLink.PATH_SEPARATOR);
        this.modelType = pathParams[3];
        this.modelName = pathParams[4];
        this.rootId = UUID.fromString(pathParams[5]);
        this.version = Integer.valueOf(pathParams[6]);
    }

    public ManualProcedureLink(String modelType, String modelName, UUID id) {
        this.type = "manualHistory";
        this.modelType = modelType;
        this.modelName = modelName;
        this.rootId = id;
        this.version = 1;
    }

    public String toURI() {
        return String.format(ENTITY_URI_FORMAT, getModelType(), getModelName(), getRootId(), getVersion());
    }

    public String getType() {
        return type;
    }

    public String getModelName() {
        return modelName;
    }

    public String getModelType() {
        return modelType;
    }

    public UUID getRootId() {
        return rootId;
    }

    public Integer getVersion() {
        return version;
    }
}
