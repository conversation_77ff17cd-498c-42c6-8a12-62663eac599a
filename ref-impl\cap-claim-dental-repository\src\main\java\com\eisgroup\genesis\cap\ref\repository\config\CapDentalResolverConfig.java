/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import com.eisgroup.genesis.cap.ref.repository.CapDentalProviderResolver;
import com.eisgroup.genesis.http.factory.HttpClientFactory;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;

/**
 * Configuration class containing resolver beans for dental claims.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalResolverConfig {

    @Bean
    public CapDentalProviderResolver capDentalProviderResolver(HttpClientFactory httpClientFactory,
            @Value("${genesis.cem.app.url:null}") String cemUrl,
            ServiceAccessRunner serviceAccessRunner) {
        return new CapDentalProviderResolver(httpClientFactory.createSecured(), cemUrl, serviceAccessRunner);
    }
}
