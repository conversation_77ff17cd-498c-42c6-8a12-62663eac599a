/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.columnstore.ColumnStore;
import com.eisgroup.genesis.columnstore.statement.ReadStatement;
import com.eisgroup.genesis.columnstore.statement.StatementBuilderFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentindex.CapDentalPaymentIdxEntity;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Default {@link CapDentalPaymentIndexRepository} implementation
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalPaymentIndexRepositoryImpl implements CapDentalPaymentIndexRepository {

    private final ColumnStore columnStore;
    private final StatementBuilderFactory statementFactory;

    public CapDentalPaymentIndexRepositoryImpl(ColumnStore columnStore, StatementBuilderFactory statementFactory) {
        this.columnStore = columnStore;
        this.statementFactory = statementFactory;
    }

    @Override
    public Streamable<List<CapDentalPaymentIdxEntity>> loadPaymentIndexesByState(String applicableState) {
        ReadStatement<CapDentalPaymentIdxEntity> rsPayments = statementFactory.read(CapDentalPaymentIdxEntity.class)
                .enableBatch()
                .joinAll()
                .build();

        return columnStore.execute("CapDentalPaymentDefinition", rsPayments)
                .filter(index -> applicableState.equals(index.getState())).buffer(1000).map(s -> s.collect(Collectors.toList()))
                .filter(sourceIndexEntities -> sourceIndexEntities.iterator().hasNext());
    }
}
