/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.dental.link.config;

import com.eisgroup.genesis.cap.adjudication.repository.ClaimSettlementRepository;
import com.eisgroup.genesis.cap.ref.dental.link.CapDentalClaimProcedureLinkBuilder;
import com.eisgroup.genesis.cap.ref.dental.link.CapDentalManualProcedureLinkBuilder;
import com.eisgroup.genesis.cap.ref.dental.link.CapDentalProcedureLinkResolver;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import org.springframework.context.annotation.Bean;

public class CapDentalLinkResolverConfig {

    @Bean
    public CapDentalProcedureLinkResolver capDentalPatientHistoryLinkResolver(CapDentalPatientHistoryRepository capDentalPatientHistoryRepository,
                                                                              ClaimSettlementRepository claimSettlementRepository) {
        return new CapDentalProcedureLinkResolver(capDentalPatientHistoryRepository, claimSettlementRepository);
    }

    @Bean
    public CapDentalManualProcedureLinkBuilder capDentalPatientHistoryLinkBuilder() {
        return new CapDentalManualProcedureLinkBuilder();
    }

    @Bean
    public CapDentalClaimProcedureLinkBuilder capDentalClaimProcedureLinkBuilder() {
        return new CapDentalClaimProcedureLinkBuilder();
    }
}
