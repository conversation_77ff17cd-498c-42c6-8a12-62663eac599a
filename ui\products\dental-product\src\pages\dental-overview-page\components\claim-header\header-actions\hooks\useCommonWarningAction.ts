import {useCallback, useState} from 'react'

import {createFormSelector} from '@eisgroup/dental-core'
import {CapDentalLoss} from '@eisgroup/dental-models'

import DentalLoss = CapDentalLoss.CapDentalLossEntity

export const useCommonWarningAction = (
    actionImpletation: (rootId: string, revisionNo: number) => Promise<void>,
    closeDrawer: () => void
) => {
    const [loading, setLoading] = useState<boolean>(false)

    const loss = createFormSelector<{entity: DentalLoss}, DentalLoss>(state => state.entity)()

    const onConfirmAction = useCallback(() => {
        setLoading(true)
        actionImpletation(loss._key?.rootId, loss?._key?.revisionNo).finally(() => {
            closeDrawer()
            setLoading(false)
        })
    }, [loss?._key?.rootId, loss._key?.revisionNo])

    return {
        loading,
        onConfirmAction
    }
}
