{
  "data": [
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_IndividualDeductible_Annual_Dental",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_FamilyDeductible_Annual_Dental",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_IndividualMaximum_Annual_Dental",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_CosmeticMaximum_Annual_CosmeticServices",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_TMJMaximum_Lifetime_TMJ",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_ImplantsMaximum_Annual_Implants",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 100,
      "extension": {
        "networkType": "INN",
        "term": {
          "effectiveDate": "2020-10-27T13:44:24.154Z",
          "expirationDate": "2022-10-27T13:44:24.154Z"
        }
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "type": "DentalPolicy_OrthoMaximum_Annual_Orthodontics",
      "transactionDate": ignore,
      "policyTermDetails": {
        "effectiveDate": "2020-10-27T13:44:24.154Z",
        "expirationDate": "2022-10-27T13:44:24.154Z",
        "_type": "Term"
      },
      "_type": "CapAccumulatorTransactionData"
    }
  ],
  "sourceURI": "gentity://UnverifiedPolicy/CapDentalUnverifiedPolicy//4c12931a-f502-4bb4-833e-cc5e79d8d4d5/1",
  "policyURI": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//4c12931a-f502-4bb4-833e-cc5e79d8d4d5",
  "customerURI": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2",
  "transactionTimestamp": ignore,
  "_modelName": "CapAccumulatorTransaction",
  "_modelVersion": "1",
  "_modelType": "CapAccumulatorTransactionEntry",
  "_type": "CapAccumulatorTransactionEntryEntity"
}
