warning package.json: No license field
$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/services/backoffice-services-impl[39m

 [32m✓[39m test/customer-service/ClaimOrganiztionCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimIndividualCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 15[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCustomerRelationshipsServiceImpl.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 15[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCensusServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 18[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimProviderServiceImpl.test.ts [2m([22m[2m16 tests[22m[2m)[22m[90m 24[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPUserTaskService.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimPolicyServiceImpl.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 19[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCustomerCommonServiceImpl.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 25[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimDentalPolicyServiceImpl.test.ts [2m([22m[2m29 tests[22m[2m)[22m[90m 32[2mms[22m[39m
 [32m✓[39m test/siderbar-service/efolder-service/DXPEfolderService.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/siderbar-service/bam-service/DXPActivityService.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 18[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPQueueService.test.ts [2m([22m[2m9 tests[22m[2m)[22m[90m 11[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPCaseService.test.ts [2m([22m[2m10 tests[22m[2m)[22m[90m 13[2mms[22m[39m
 [32m✓[39m test/siderbar-service/note-service/DXPNoteService.test.ts [2m([22m[2m35 tests[22m[2m)[22m[90m 32[2mms[22m[39m

[2m Test Files [22m [1m[32m14 passed[39m[22m[90m (14)[39m
[2m      Tests [22m [1m[32m179 passed[39m[22m[90m (179)[39m
[2m   Start at [22m 15:01:15
[2m   Duration [22m 26.85s[2m (transform 6.61s, setup 181.36s, collect 28.25s, tests 260ms, environment 84.30s, prepare 4.17s)[22m

