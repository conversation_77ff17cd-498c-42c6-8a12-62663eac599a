/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import java.util.Optional;

import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.google.gson.JsonObject;

/**
 * Resolver for CEM provider.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalProviderResolver {

    private static final String CEM_PROVIDER_LOAD_ENDPOINT = "api/common/Provider/v1/load/";

    private final String cemUrl;
    private final HttpClient httpClient;
    private final ServiceAccessRunner serviceAccessRunner;

    public CapDentalProviderResolver(HttpClient httpClient, String cemUrl, ServiceAccessRunner serviceAccessRunner) {
        this.cemUrl = cemUrl;
        this.httpClient = httpClient;
        this.serviceAccessRunner = serviceAccessRunner;
    }

    /**
     * Retrieves provider using REST services.
     *
     * @param rootId provider identifier
     *
     * @return JSON representation of provider as provider belongs to CEM domain.
     */
    public Lazy<JsonObject> findProvider(String rootId) {

        HttpPost httpRequest = new HttpPost(createFullUrl(rootId));
        return serviceAccessRunner.runLazy(() -> {
            JsonObject provider = httpClient.execute(httpRequest, new JsonResponseHandler())
                .getAsJsonObject()
                .getAsJsonObject("body")
                .getAsJsonObject("success");
            return Lazy.of(provider);
        });
    }

    private String createFullUrl(String providerId) {
        return Optional.ofNullable(cemUrl)
                .map(url -> url.endsWith("/") ? url : url + "/")
                .map(url -> url + CEM_PROVIDER_LOAD_ENDPOINT + providerId)
                .orElseThrow(() -> new UnsupportedOperationException("cemUrl is not configured for LoadProvider."));
    }
}
