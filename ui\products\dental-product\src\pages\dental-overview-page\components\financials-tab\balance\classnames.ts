import {withPrefix} from '../../../../../utils'

export const BALANCE_TAB = withPrefix('balance-tab')
export const BALANCE_HEADER = withPrefix('balance-header')
export const BALANCE_ACTIVITIES = withPrefix('balance-activities')
export const BALANCE_ACTIVITIES_TABLE = withPrefix('balance-activities-table')
export const BALANCE_ACTIVITIES_COLUMN_DESC = withPrefix('balance-activities-column-desc')
export const BALANCE_DESCRIPTION_ELLIPSIS = withPrefix('balance-description-ellipsis')
export const OVERPAYMENT_AMOUNT = withPrefix('overpayment-amount')
export const BALANCE_HEADER_RIGHT_SECTION = withPrefix('balance-header-right-section')
export const BALANCE_HEADER_AMOUNT_BOX = withPrefix('balance-header-amount-box')
export const BALANCE_HEADER_RIGHT_SECTION_TOOLTIP = withPrefix('balance-header-right-section-tooltip')

export const OVERPAYMENT_AMOUNT_VALUE = withPrefix('overpayment-amount-value')
export const OVERPAYMENT_AMOUNT_LABEL = withPrefix('overpayment-amount-label')
export const SELECT_ACTION = withPrefix('select-action')
export const RECALCULATED_PAYMENTS = withPrefix('recalculated-payments')
export const RECALCULATED_PAYMENTS_HEADER = withPrefix('recalculated-payments-header')
export const RECALCULATED_PAYMENTS_SUMMARY = withPrefix('recalculated-payments-summary')
export const RECALCULATED_PAYMENTS_SUMMARY_CARD = withPrefix('recalculated-payments-summary-card')
export const RECALCULATED_PAYMENTS_SUMMARY_CARD_TITLE = withPrefix('recalculated-payments-summary-card-title')
export const RECALCULATED_PAYMENTS_SUMMARY_CARD_DATA = withPrefix('recalculated-payments-summary-card-data')
export const RECALCULATED_PAYMENTS_TABLE = withPrefix('recalculated-payments-table')
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND = withPrefix('recalculated-payments-allocation-table-expand')
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ROW = withPrefix(
    'recalculated-payments-allocation-table-expand-row'
)
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_PAYMENT_AMOUNT = withPrefix(
    'recalculated-payments-allocation-table-expand-payment-amount'
)
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_SHOULD_HAVE_PAID = withPrefix(
    'recalculated-payments-allocation-table-expand-should-have-paid'
)
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION = withPrefix(
    'recalculated-payments-allocation-table-expand-allocation'
)
export const RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST = withPrefix(
    'recalculated-payments-allocation-table-expand-interest'
)

export const TRANSACTION_AMOUNT_NEGATIVE = withPrefix('transaction-amount-negative')
export const TRANSACTION_AMOUNT_POSITIVE = withPrefix('transaction-amount-positive')
export const BALANCE_NEGATIVE = withPrefix('balance-negative')
export const BALANCE_POSITIVE = withPrefix('balance-positive')
