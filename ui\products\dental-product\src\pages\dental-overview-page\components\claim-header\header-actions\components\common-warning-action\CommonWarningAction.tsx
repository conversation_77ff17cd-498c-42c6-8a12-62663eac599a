import React, {FC} from 'react'

import {useTranslate} from '@eisgroup/i18n'
import {Modal} from '@eisgroup/ui-kit'

import {COMMON_WARNING_ACTION_MODAL} from './classnames'

interface CommonWarningActionProps {
    visible: boolean
    title: React.ReactNode
    content: React.ReactNode
    onOk?: () => void
    onClose?: () => void
    comfirmLoading?: boolean
}

export const CommonWarningAction: FC<CommonWarningActionProps> = props => {
    const {visible, title, content, onOk, onClose, comfirmLoading} = props
    const {t} = useTranslate()
    return (
        <Modal
            className={COMMON_WARNING_ACTION_MODAL}
            type='warning'
            visible={visible}
            title={title}
            onOk={onOk}
            onCancel={onClose}
            okText={t('dental-product:confirm')}
            confirmLoading={comfirmLoading}
            closable
        >
            <div>{content}</div>
        </Modal>
    )
}
