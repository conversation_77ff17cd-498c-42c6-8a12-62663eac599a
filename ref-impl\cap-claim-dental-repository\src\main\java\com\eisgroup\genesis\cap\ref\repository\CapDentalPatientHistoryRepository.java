/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.repository;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;

/**
 * Repository to store and retrieve dental patient history.
 *
 * <AUTHOR>
 * @since 22.6
 */
public interface CapDentalPatientHistoryRepository {

    /**
     * Saves dental patient history to storage.
     *
     * @param patientHistory patient history to be saved
     * @return saved patient history
     *
     * <AUTHOR>
     * @since 22.6
     */
    Lazy<CapDentalPatientHistoryEntity> save(CapDentalPatientHistoryEntity patientHistory);

    /**
     * Retrieves dental patient history record from storage.
     *
     * @param key identifier key of patient history, which should be retrieved
     * @param modelName used to resolve correct storage
     * @return loaded patient history or error if such entity was not found
     *
     * <AUTHOR>
     * @since 22.6
     */
    Lazy<CapDentalPatientHistoryEntity> load(RootEntityKey key, String modelName);

}
